<?php

declare(strict_types=1);

use Tests\TestCase;

// Base test case for all tests
uses(TestCase::class)->in('Feature');
uses(TestCase::class)->in('Unit');

// Custom expectations for validate-links
expect()->extend('toBeValidUrl', function () {
    return $this->toMatch('/^https?:\/\/.+/');
});

expect()->extend('toBeValidMarkdown', function () {
    return $this->toContain('# ');
});

expect()->extend('toBeValidationResult', function () {
    return $this->toBeInstanceOf(App\Services\ValueObjects\ValidationResult::class);
});

expect()->extend('toHaveBrokenLinks', function (?int $count = null) {
    $brokenLinks = $this->value->getBrokenLinks();

    if ($count === null) {
        return expect($brokenLinks)->not->toBeEmpty();
    }

    return expect($brokenLinks)->toHaveCount($count);
});
