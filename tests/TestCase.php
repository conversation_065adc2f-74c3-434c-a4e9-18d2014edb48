<?php

declare(strict_types=1);

namespace Tests;

use LaravelZero\Framework\Testing\TestCase as BaseTestCase;
use Tests\Traits\CreatesApplication;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Only essential setup here
        config(['validate-links.cache.enabled' => false]);
        config(['validate-links.performance.memory_limit' => '512M']);
        config(['validate-links.external.timeout' => 5]);
    }

    protected function tearDown(): void
    {
        // Clean up any global state
        parent::tearDown();
    }
}
