<?php

// tests/Unit/Providers/ValidateLinksServiceProviderTest.php

declare(strict_types=1);

namespace Tests\Unit\Providers;

use App\Providers\ValidateLinksServiceProvider;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\LinkValidationService;
use Tests\TestCase;

final class ValidateLinksServiceProviderTest extends TestCase
{
    public function test_services_are_registered(): void
    {
        $provider = new ValidateLinksServiceProvider($this->app);
        $provider->register();

        $this->assertTrue($this->app->bound(LinkValidationInterface::class));
        $this->assertInstanceOf(
            LinkValidationService::class,
            $this->app->make(LinkValidationInterface::class)
        );
    }

    public function test_singleton_services_return_same_instance(): void
    {
        $provider = new ValidateLinksServiceProvider($this->app);
        $provider->register();

        $instance1 = $this->app->make(LinkValidationInterface::class);
        $instance2 = $this->app->make(LinkValidationInterface::class);

        $this->assertSame($instance1, $instance2);
    }
}
