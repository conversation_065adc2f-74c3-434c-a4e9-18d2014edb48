<?php

declare(strict_types=1);
// tests/Unit/Services/LinkValidationServiceTest.php

use App\Services\LinkValidationService;
use Tests\TestCase;
use Tests\Traits\FileTestHelpers;
use Tests\Traits\HttpTestHelpers;
use Tests\Traits\ValidationTestHelpers;

uses(TestCase::class);

describe('LinkValidationService', function () {
    beforeEach(function () {
        $this->service = app(LinkValidationService::class);
    });

    describe('internal link validation', function () {
        uses(FileTestHelpers::class, ValidationTestHelpers::class);

        it('validates existing internal links', function () {
            // Create test files using FileTestHelpers trait
            $this->createTestFile('docs/test.md', '# Test Document');
            $this->createTestMarkdownFile('docs/guide.md', [
                'Test Link' => 'test.md',
                'Another Link' => 'test.md#section',
            ]);

            $config = $this->createValidationConfig(['scopes' => ['internal']]);
            $result = $this->service->validateFile('docs/guide.md', $config);

            $this->assertValidationSuccess($result);
        });

        it('detects broken internal links', function () {
            $this->createTestMarkdownFile('docs/guide.md', [
                'Broken Link' => 'missing.md',
            ]);

            $config = $this->createValidationConfig(['scopes' => ['internal']]);
            $result = $this->service->validateFile('docs/guide.md', $config);

            $this->assertValidationFailure($result, 1);
        });

        afterEach(function () {
            $this->cleanupTestFiles();
        });
    });

    describe('external link validation', function () {
        uses(HttpTestHelpers::class, ValidationTestHelpers::class);

        it('validates working external links', function () {
            $urls = ['https://example.com', 'https://github.com'];
            $this->mockSuccessfulHttpResponses($urls);

            $config = $this->createValidationConfig(['scopes' => ['external']]);
            $result = $this->service->validateExternalLinks($urls, $config);

            $this->assertValidationSuccess($result);
        });

        it('handles various HTTP error scenarios', function () {
            $this->mockMixedHttpResponses([
                'https://not-found.example' => ['status' => 404],
                'https://server-error.example' => ['status' => 500],
                'https://forbidden.example' => ['status' => 403],
            ]);

            $urls = array_keys($this->mockMixedHttpResponses);
            $config = $this->createValidationConfig(['scopes' => ['external']]);
            $result = $this->service->validateExternalLinks($urls, $config);

            $this->assertValidationFailure($result, 3);
        });
    });
});
