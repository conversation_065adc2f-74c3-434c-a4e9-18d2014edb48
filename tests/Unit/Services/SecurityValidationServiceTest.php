<?php

// tests/Unit/Services/SecurityValidationServiceTest.php

declare(strict_types=1);

use App\Services\SecurityValidationService;

describe('SecurityValidationService', function () {
    beforeEach(function () {
        $this->service = new SecurityValidationService();
    });

    describe('validatePath', function () {
        it('validates safe paths', function () {
            expect($this->service->validatePath('./docs/test.md'))->toBeTrue();
        });

        it('rejects path traversal attempts', function () {
            expect($this->service->validatePath('../../../etc/passwd'))->toBeFalse();
        });
    });

    describe('validateUrl', function () {
        it('validates https URLs', function () {
            expect($this->service->validateUrl('https://example.com'))->toBeTrue();
        });

        it('rejects invalid protocols', function () {
            expect($this->service->validateUrl('ftp://example.com'))->toBeFalse();
        });
    });
});
