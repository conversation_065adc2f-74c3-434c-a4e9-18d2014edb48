<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

use App\Enums\LinkStatus;
use Tests\Traits\EnumTestHelpers;

uses(EnumTestHelpers::class);

describe('LinkStatus Enum', function () {

    test('has all expected cases with correct values', function () {
        $expectedCases = [
            'VALID' => 'valid',
            'BROKEN' => 'broken',
            'TIMEOUT' => 'timeout',
            'FORBIDDEN' => 'forbidden',
            'NOT_FOUND' => 'not_found',
            'INVALID_FORMAT' => 'invalid_format',
            'SECURITY_VIOLATION' => 'security_violation',
            'REDIRECT_LOOP' => 'redirect_loop',
            'SSL_ERROR' => 'ssl_error',
            'DNS_ERROR' => 'dns_error',
        ];

        $this->assertEnumCasesMatch(LinkStatus::class, $expectedCases);
    });

    test('values() returns correct array', function () {
        $expectedValues = [
            'valid', 'broken', 'timeout', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop',
            'ssl_error', 'dns_error'
        ];

        $this->assertEnumValuesMethod(LinkStatus::class, $expectedValues);
    });

    test('cases() returns all enum cases', function () {
        $cases = LinkStatus::cases();

        expect($cases)->toHaveCount(10);
        expect($cases)->each->toBeInstanceOf(LinkStatus::class);
    });

    test('tryFrom() works correctly', function () {
        $validValues = [
            'valid', 'broken', 'timeout', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop',
            'ssl_error', 'dns_error'
        ];
        $invalidValues = ['invalid', 'unknown', '', 'VALID', 'Valid'];

        $this->assertEnumTryFromMethod(LinkStatus::class, $validValues, $invalidValues);
    });

    test('from() works correctly and throws for invalid values', function () {
        $validValues = [
            'valid', 'broken', 'timeout', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop',
            'ssl_error', 'dns_error'
        ];
        $invalidValues = ['invalid', 'unknown', '', 'VALID', 'Valid'];

        $this->assertEnumFromMethod(LinkStatus::class, $validValues, $invalidValues);
    });

    test('isBroken() returns correct values', function () {
        $trueCases = [
            'broken', 'timeout', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop',
            'ssl_error', 'dns_error'
        ];
        $falseCases = ['valid'];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'isBroken', $trueCases, $falseCases);
    });

    test('isTemporary() returns correct values', function () {
        $trueCases = ['timeout', 'dns_error'];
        $falseCases = [
            'valid', 'broken', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop', 'ssl_error'
        ];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'isTemporary', $trueCases, $falseCases);
    });

    test('isSecurityIssue() returns correct values', function () {
        $trueCases = ['security_violation', 'ssl_error'];
        $falseCases = [
            'valid', 'broken', 'timeout', 'forbidden', 'not_found',
            'invalid_format', 'redirect_loop', 'dns_error'
        ];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'isSecurityIssue', $trueCases, $falseCases);
    });

    test('shouldRetry() returns correct values', function () {
        $trueCases = ['timeout', 'dns_error'];
        $falseCases = [
            'valid', 'broken', 'forbidden', 'not_found',
            'invalid_format', 'security_violation', 'redirect_loop', 'ssl_error'
        ];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'shouldRetry', $trueCases, $falseCases);
    });

    test('getDescription() returns non-empty strings', function () {
        $this->assertEnumDescriptionsNotEmpty(LinkStatus::class);
    });

    test('getDescription() returns expected descriptions', function () {
        $expectedDescriptions = [
            'valid' => 'Link is valid and accessible',
            'broken' => 'Link is broken or inaccessible',
            'timeout' => 'Request timed out',
            'forbidden' => 'Access forbidden (403)',
            'not_found' => 'Resource not found (404)',
            'invalid_format' => 'Invalid URL format',
            'security_violation' => 'Security policy violation',
            'redirect_loop' => 'Redirect loop detected',
            'ssl_error' => 'SSL/TLS certificate error',
            'dns_error' => 'DNS resolution failed'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getDescription', $expectedDescriptions);
    });

    test('getSeverity() returns correct integer values', function () {
        $expectedSeverities = [
            'valid' => 0,
            'timeout' => 2,
            'dns_error' => 2,
            'not_found' => 3,
            'invalid_format' => 3,
            'forbidden' => 4,
            'redirect_loop' => 4,
            'broken' => 5,
            'security_violation' => 5,
            'ssl_error' => 5
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getSeverity', $expectedSeverities);
    });

    test('getSeverity() returns values in valid range', function () {
        $this->assertEnumIntegerMethod(LinkStatus::class, 'getSeverity', 0, 5);
    });

    test('getRecommendedAction() returns non-empty strings', function () {
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getRecommendedAction', 'string');

        foreach (LinkStatus::cases() as $case) {
            $action = $case->getRecommendedAction();
            expect($action)->not->toBeEmpty("Recommended action for case {$case->name} should not be empty");
        }
    });

    test('getRecommendedAction() returns expected actions', function () {
        $expectedActions = [
            'valid' => 'No action required',
            'timeout' => 'Retry validation or increase timeout',
            'forbidden' => 'Check access permissions or authentication',
            'not_found' => 'Update link URL or remove broken link',
            'invalid_format' => 'Fix URL syntax and format',
            'security_violation' => 'Review security policies and URL safety',
            'redirect_loop' => 'Check redirect configuration',
            'ssl_error' => 'Verify SSL certificate validity',
            'dns_error' => 'Check domain name and DNS configuration',
            'broken' => 'Investigate and fix underlying issue'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getRecommendedAction', $expectedActions);
    });

    test('getConsoleColor() returns valid color names', function () {
        $expectedColors = [
            'valid' => 'green',
            'timeout' => 'yellow',
            'dns_error' => 'yellow',
            'not_found' => 'magenta',
            'invalid_format' => 'magenta',
            'forbidden' => 'cyan',
            'redirect_loop' => 'cyan',
            'broken' => 'red',
            'security_violation' => 'red',
            'ssl_error' => 'red'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getConsoleColor', $expectedColors);
    });

    test('getIcon() returns emoji icons', function () {
        $expectedIcons = [
            'valid' => '✅',
            'timeout' => '⏱️',
            'forbidden' => '🚫',
            'not_found' => '❌',
            'invalid_format' => '⚠️',
            'security_violation' => '🔒',
            'redirect_loop' => '🔄',
            'ssl_error' => '🔐',
            'dns_error' => '🌐',
            'broken' => '💥'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getIcon', $expectedIcons);
    });

    test('getGroup() returns correct groupings', function () {
        $expectedGroups = [
            'valid' => 'success',
            'timeout' => 'temporary',
            'dns_error' => 'temporary',
            'forbidden' => 'client_error',
            'not_found' => 'client_error',
            'invalid_format' => 'format_error',
            'security_violation' => 'format_error',
            'redirect_loop' => 'protocol_error',
            'ssl_error' => 'protocol_error',
            'broken' => 'unknown_error'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getGroup', $expectedGroups);
    });

    test('getHttpStatusCode() returns correct nullable integers', function () {
        $expectedStatusCodes = [
            'valid' => 200,
            'forbidden' => 403,
            'not_found' => 404,
            'timeout' => 408,
            'redirect_loop' => 310,
            'ssl_error' => 526,
            'broken' => null,
            'invalid_format' => null,
            'security_violation' => null,
            'dns_error' => null
        ];

        $this->assertEnumNullableIntegerMethod(LinkStatus::class, 'getHttpStatusCode', $expectedStatusCodes);
    });

    test('getFormattedDisplay() returns formatted strings', function () {
        foreach (LinkStatus::cases() as $case) {
            $display = $case->getFormattedDisplay();

            expect($display)->toBeString();
            expect($display)->not->toBeEmpty();
            expect($display)->toContain('<fg=');
            expect($display)->toContain($case->getIcon());
            expect($display)->toContain($case->getDescription());
            expect($display)->toContain($case->getConsoleColor());
        }
    });

    test('serialization works correctly', function () {
        $this->assertEnumSerialization(LinkStatus::class);
    });

    test('comparison operations work correctly', function () {
        $this->assertEnumComparisons(LinkStatus::class);
    });

    test('all methods return consistent types', function () {
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getDescription', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getRecommendedAction', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getConsoleColor', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getIcon', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getGroup', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getFormattedDisplay', 'string');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'getSeverity', 'int');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'isBroken', 'bool');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'isTemporary', 'bool');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'isSecurityIssue', 'bool');
        $this->assertEnumMethodConsistentType(LinkStatus::class, 'shouldRetry', 'bool');
    });

    // Edge case and business logic tests
    test('VALID is the only non-broken status', function () {
        expect(LinkStatus::VALID->isBroken())->toBeFalse();

        foreach (LinkStatus::cases() as $case) {
            if ($case !== LinkStatus::VALID) {
                expect($case->isBroken())->toBeTrue("Case {$case->name} should be broken");
            }
        }
    });

    test('only temporary statuses should be retried', function () {
        $temporaryStatuses = [LinkStatus::TIMEOUT, LinkStatus::DNS_ERROR];
        $nonTemporaryStatuses = array_filter(
            LinkStatus::cases(),
            fn($case) => !in_array($case, $temporaryStatuses, true)
        );

        foreach ($temporaryStatuses as $status) {
            expect($status->isTemporary())->toBeTrue();
            expect($status->shouldRetry())->toBeTrue();
        }

        foreach ($nonTemporaryStatuses as $status) {
            expect($status->isTemporary())->toBeFalse();
            expect($status->shouldRetry())->toBeFalse();
        }
    });

    test('security issues have high severity', function () {
        $securityStatuses = [LinkStatus::SECURITY_VIOLATION, LinkStatus::SSL_ERROR];

        foreach ($securityStatuses as $status) {
            expect($status->isSecurityIssue())->toBeTrue();
            expect($status->getSeverity())->toBe(5, "Security issue {$status->name} should have maximum severity");
        }
    });

    test('severity levels are logically ordered', function () {
        expect(LinkStatus::VALID->getSeverity())->toBe(0); // No issue
        expect(LinkStatus::TIMEOUT->getSeverity())->toBeLessThan(LinkStatus::NOT_FOUND->getSeverity());
        expect(LinkStatus::NOT_FOUND->getSeverity())->toBeLessThan(LinkStatus::FORBIDDEN->getSeverity());
        expect(LinkStatus::FORBIDDEN->getSeverity())->toBeLessThan(LinkStatus::BROKEN->getSeverity());
    });

    test('HTTP status codes are valid when present', function () {
        $validHttpCodes = [200, 310, 403, 404, 408, 526];

        foreach (LinkStatus::cases() as $case) {
            $httpCode = $case->getHttpStatusCode();
            if ($httpCode !== null) {
                expect($httpCode)->toBeIn($validHttpCodes, "HTTP status code {$httpCode} for {$case->name} should be valid");
                expect($httpCode)->toBeGreaterThanOrEqual(200);
                expect($httpCode)->toBeLessThanOrEqual(599);
            }
        }
    });

    test('console colors are valid CSS/terminal colors', function () {
        $validColors = ['green', 'yellow', 'magenta', 'cyan', 'red'];

        foreach (LinkStatus::cases() as $case) {
            $color = $case->getConsoleColor();
            expect($color)->toBeIn($validColors, "Color {$color} for {$case->name} should be a valid terminal color");
        }
    });

    test('groups categorize statuses logically', function () {
        // Success group
        expect(LinkStatus::VALID->getGroup())->toBe('success');

        // Temporary issues
        $temporaryGroup = array_filter(LinkStatus::cases(), fn($case) => $case->getGroup() === 'temporary');
        foreach ($temporaryGroup as $case) {
            expect($case->isTemporary())->toBeTrue("Case {$case->name} in temporary group should be temporary");
        }

        // Client errors should have 4xx-like status codes or be client-side issues
        $clientErrorGroup = array_filter(LinkStatus::cases(), fn($case) => $case->getGroup() === 'client_error');
        foreach ($clientErrorGroup as $case) {
            $httpCode = $case->getHttpStatusCode();
            if ($httpCode !== null) {
                expect($httpCode)->toBeGreaterThanOrEqual(400);
                expect($httpCode)->toBeLessThan(500);
            }
        }
    });
});
