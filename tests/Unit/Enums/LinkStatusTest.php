<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

test('values', function () {});
test('get group', function () {});
test('get icon', function () {});
test('try from',
    function () {}, );
test('cases', function () {});
test('get severity', function () {});
test('is security issue',
    function () {}, );
test('get console color', function () {});
test('is temporary', function () {});
test('should retry',
    function () {}, );
test('get recommended action', function () {});
test('get description',
    function () {}, );
test('get http status code', function () {});
test('get formatted display',
    function () {}, );
test('from', function () {});
test('is broken', function () {});
