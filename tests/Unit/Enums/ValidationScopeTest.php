<?php

declare(strict_types=1);

namespace Tests\Feature\Enums;

test('get description', function () {});
test('values', function () {});
test('includes external',
    function () {}, );
test('get included scopes', function () {});
test('from string',
    function () {}, );
test('includes internal', function () {});
test('includes anchor',
    function () {}, );
test('includes image', function () {});
test('try from', function () {});
test('cases',
    function () {}, );
test('get priority', function () {});
test('get select options', function () {});
test('is valid',
    function () {}, );
test('names', function () {});
test('get help text', function () {});
test('from', function () {});
