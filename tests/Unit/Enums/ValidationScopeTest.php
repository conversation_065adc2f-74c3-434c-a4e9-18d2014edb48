<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

use App\Enums\ValidationScope;
use Tests\Traits\EnumTestHelpers;

uses(EnumTestHelpers::class);

describe('ValidationScope Enum', function () {

    test('has all expected cases with correct values', function () {
        $expectedCases = [
            'INTERNAL' => 'internal',
            'EXTERNAL' => 'external',
            'ANCHOR' => 'anchor',
            'IMAGE' => 'image',
            'ALL' => 'all',
        ];

        $this->assertEnumCasesMatch(ValidationScope::class, $expectedCases);
    });

    test('values() returns correct array', function () {
        $expectedValues = ['internal', 'external', 'anchor', 'image', 'all'];

        $this->assertEnumValuesMethod(ValidationScope::class, $expectedValues);
    });

    test('names() returns correct array', function () {
        $expectedNames = ['INTERNAL', 'EXTERNAL', 'ANCHOR', 'IMAGE', 'ALL'];

        $this->assertEnumNamesMethod(ValidationScope::class, $expectedNames);
    });

    test('cases() returns all enum cases', function () {
        $cases = ValidationScope::cases();

        expect($cases)->toHaveCount(5);
        expect($cases)->each->toBeInstanceOf(ValidationScope::class);
    });

    test('tryFrom() works correctly', function () {
        $validValues = ['internal', 'external', 'anchor', 'image', 'all'];
        $invalidValues = ['invalid', 'unknown', '', 'INTERNAL', 'Internal', 'external_links'];

        $this->assertEnumTryFromMethod(ValidationScope::class, $validValues, $invalidValues);
    });

    test('from() works correctly and throws for invalid values', function () {
        $validValues = ['internal', 'external', 'anchor', 'image', 'all'];
        $invalidValues = ['invalid', 'unknown', '', 'INTERNAL', 'Internal', 'external_links'];

        $this->assertEnumFromMethod(ValidationScope::class, $validValues, $invalidValues);
    });

    test('isValid() static method works correctly', function () {
        $validValues = ['internal', 'external', 'anchor', 'image', 'all'];
        $invalidValues = ['invalid', 'unknown', '', 'INTERNAL', 'Internal', 'external_links'];

        $this->assertEnumIsValidMethod(ValidationScope::class, $validValues, $invalidValues);
    });

    test('fromString() works correctly with case conversion', function () {
        $validInputs = [
            'internal' => 'internal',
            'INTERNAL' => 'internal',
            'Internal' => 'internal',
            'EXTERNAL' => 'external',
            'external' => 'external',
            'External' => 'external',
            'ALL' => 'all',
            'all' => 'all',
            'All' => 'all'
        ];
        $invalidInputs = ['invalid', 'unknown', '', 'external_links', 'internal_only'];

        $this->assertEnumFromStringMethod(ValidationScope::class, $validInputs, $invalidInputs);
    });

    test('getSelectOptions() returns properly formatted array', function () {
        $this->assertEnumSelectOptionsMethod(ValidationScope::class);

        $options = ValidationScope::getSelectOptions();
        expect($options)->toHaveCount(5);

        // Verify each option has the correct structure
        foreach ($options as $option) {
            expect($option)->toHaveKeys(['value', 'label']);
            expect(ValidationScope::isValid($option['value']))->toBeTrue();
        }
    });

    test('includesInternal() returns correct values', function () {
        $trueCases = ['internal', 'all'];
        $falseCases = ['external', 'anchor', 'image'];

        $this->assertEnumBooleanMethod(ValidationScope::class, 'includesInternal', $trueCases, $falseCases);
    });

    test('includesExternal() returns correct values', function () {
        $trueCases = ['external', 'all'];
        $falseCases = ['internal', 'anchor', 'image'];

        $this->assertEnumBooleanMethod(ValidationScope::class, 'includesExternal', $trueCases, $falseCases);
    });

    test('includesAnchor() returns correct values', function () {
        $trueCases = ['anchor', 'all'];
        $falseCases = ['internal', 'external', 'image'];

        $this->assertEnumBooleanMethod(ValidationScope::class, 'includesAnchor', $trueCases, $falseCases);
    });

    test('includesImage() returns correct values', function () {
        $trueCases = ['image', 'all'];
        $falseCases = ['internal', 'external', 'anchor'];

        $this->assertEnumBooleanMethod(ValidationScope::class, 'includesImage', $trueCases, $falseCases);
    });

    test('getDescription() returns non-empty strings', function () {
        $this->assertEnumDescriptionsNotEmpty(ValidationScope::class);
    });

    test('getHelpText() returns non-empty strings', function () {
        $this->assertEnumHelpTextNotEmpty(ValidationScope::class);
    });

    test('getIncludedScopes() returns correct arrays', function () {
        $expectedIncludedScopes = [
            'internal' => [ValidationScope::INTERNAL],
            'external' => [ValidationScope::EXTERNAL],
            'anchor' => [ValidationScope::ANCHOR],
            'image' => [ValidationScope::IMAGE],
            'all' => [ValidationScope::INTERNAL, ValidationScope::EXTERNAL, ValidationScope::ANCHOR, ValidationScope::IMAGE]
        ];

        $this->assertEnumArrayMethod(ValidationScope::class, 'getIncludedScopes', $expectedIncludedScopes);
    });

    test('getPriority() returns correct integer values', function () {
        $expectedPriorities = [
            'internal' => 1,
            'anchor' => 2,
            'image' => 3,
            'external' => 4,
            'all' => 0
        ];

        $this->assertEnumMethodReturns(ValidationScope::class, 'getPriority', $expectedPriorities);
    });

    test('getPriority() returns values in valid range', function () {
        $this->assertEnumIntegerMethod(ValidationScope::class, 'getPriority', 0, 4);
    });

    test('serialization works correctly', function () {
        $this->assertEnumSerialization(ValidationScope::class);
    });

    test('comparison operations work correctly', function () {
        $this->assertEnumComparisons(ValidationScope::class);
    });

    test('all methods return consistent types', function () {
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'getDescription', 'string');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'getHelpText', 'string');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'getIncludedScopes', 'array');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'getPriority', 'int');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'includesInternal', 'bool');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'includesExternal', 'bool');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'includesAnchor', 'bool');
        $this->assertEnumMethodConsistentType(ValidationScope::class, 'includesImage', 'bool');
    });

    // Business logic and edge case tests
    test('ALL scope includes all other scopes', function () {
        $allScope = ValidationScope::ALL;

        expect($allScope->includesInternal())->toBeTrue();
        expect($allScope->includesExternal())->toBeTrue();
        expect($allScope->includesAnchor())->toBeTrue();
        expect($allScope->includesImage())->toBeTrue();

        $includedScopes = $allScope->getIncludedScopes();
        expect($includedScopes)->toHaveCount(4);
        expect($includedScopes)->toContain(ValidationScope::INTERNAL);
        expect($includedScopes)->toContain(ValidationScope::EXTERNAL);
        expect($includedScopes)->toContain(ValidationScope::ANCHOR);
        expect($includedScopes)->toContain(ValidationScope::IMAGE);
    });

    test('individual scopes only include themselves', function () {
        $individualScopes = [
            ValidationScope::INTERNAL, ValidationScope::EXTERNAL,
            ValidationScope::ANCHOR, ValidationScope::IMAGE
        ];

        foreach ($individualScopes as $scope) {
            $includedScopes = $scope->getIncludedScopes();
            expect($includedScopes)->toHaveCount(1);
            expect($includedScopes[0])->toBe($scope);
        }
    });

    test('priority ordering is logical', function () {
        // ALL should have highest priority (lowest number)
        expect(ValidationScope::ALL->getPriority())->toBe(0);

        // INTERNAL should have higher priority than EXTERNAL
        expect(ValidationScope::INTERNAL->getPriority())
            ->toBeLessThan(ValidationScope::EXTERNAL->getPriority());

        // ANCHOR should have higher priority than IMAGE
        expect(ValidationScope::ANCHOR->getPriority())
            ->toBeLessThan(ValidationScope::IMAGE->getPriority());

        // EXTERNAL should have lowest priority among individual scopes
        expect(ValidationScope::EXTERNAL->getPriority())->toBe(4);
    });

    test('scope inclusion methods are mutually exclusive for individual scopes', function () {
        $testCases = [
            ValidationScope::INTERNAL => ['includesInternal' => true, 'includesExternal' => false, 'includesAnchor' => false, 'includesImage' => false],
            ValidationScope::EXTERNAL => ['includesInternal' => false, 'includesExternal' => true, 'includesAnchor' => false, 'includesImage' => false],
            ValidationScope::ANCHOR => ['includesInternal' => false, 'includesExternal' => false, 'includesAnchor' => true, 'includesImage' => false],
            ValidationScope::IMAGE => ['includesInternal' => false, 'includesExternal' => false, 'includesAnchor' => false, 'includesImage' => true],
        ];

        foreach ($testCases as $scope => $expectations) {
            foreach ($expectations as $method => $expected) {
                $result = $scope->$method();
                expect($result)->toBe($expected,
                    "Scope {$scope->value} method {$method}() should return " . ($expected ? 'true' : 'false')
                );
            }
        }
    });

    test('fromString() handles case insensitivity correctly', function () {
        $testCases = [
            'internal' => ValidationScope::INTERNAL,
            'INTERNAL' => ValidationScope::INTERNAL,
            'Internal' => ValidationScope::INTERNAL,
            'iNtErNaL' => ValidationScope::INTERNAL,
            'external' => ValidationScope::EXTERNAL,
            'EXTERNAL' => ValidationScope::EXTERNAL,
            'all' => ValidationScope::ALL,
            'ALL' => ValidationScope::ALL,
        ];

        foreach ($testCases as $input => $expectedScope) {
            $actualScope = ValidationScope::fromString($input);
            expect($actualScope)->toBe($expectedScope,
                "fromString('{$input}') should return {$expectedScope->name}"
            );
        }
    });

    test('descriptions and help text are distinct and informative', function () {
        foreach (ValidationScope::cases() as $scope) {
            $description = $scope->getDescription();
            $helpText = $scope->getHelpText();

            expect($description)->not->toBe($helpText,
                "Description and help text for {$scope->name} should be different"
            );

            expect($description)->toContain('Validate',
                "Description for {$scope->name} should mention validation"
            );

            expect($helpText)->toContain('Validates',
                "Help text for {$scope->name} should mention validation action"
            );
        }
    });
});
