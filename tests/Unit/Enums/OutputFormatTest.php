<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

test('is ci cd friendly', function () {});
test('get mime type', function () {});
test('get description',
    function () {}, );
test('supports formatting', function () {});
test('is valid',
    function () {}, );
test('get select options', function () {});
test('try from',
    function () {}, );
test('get formatter class', function () {});
test('get default filename',
    function () {}, );
test('cases', function () {});
test('get use cases', function () {});
test('is structured',
    function () {}, );
test('values', function () {});
test('get extension', function () {});
test('from',
    function () {}, );
test('get help text', function () {});
