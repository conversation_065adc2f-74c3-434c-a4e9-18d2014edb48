<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

use App\Enums\OutputFormat;
use Tests\Traits\EnumTestHelpers;

uses(EnumTestHelpers::class);

describe('OutputFormat Enum', function () {

    test('has all expected cases with correct values', function () {
        $expectedCases = [
            'CONSOLE' => 'console',
            'JSON' => 'json',
            'HTML' => 'html',
            'MARKDOWN' => 'markdown',
            'XML' => 'xml',
            'CSV' => 'csv',
        ];

        $this->assertEnumCasesMatch(OutputFormat::class, $expectedCases);
    });

    test('values() returns correct array', function () {
        $expectedValues = ['console', 'json', 'html', 'markdown', 'xml', 'csv'];

        $this->assertEnumValuesMethod(OutputFormat::class, $expectedValues);
    });

    test('cases() returns all enum cases', function () {
        $cases = OutputFormat::cases();

        expect($cases)->toHaveCount(6);
        expect($cases)->each->toBeInstanceOf(OutputFormat::class);
    });

    test('tryFrom() works correctly', function () {
        $validValues = ['console', 'json', 'html', 'markdown', 'xml', 'csv'];
        $invalidValues = ['invalid', 'unknown', '', 'JSON', 'Html', 'txt', 'pdf'];

        $this->assertEnumTryFromMethod(OutputFormat::class, $validValues, $invalidValues);
    });

    test('from() works correctly and throws for invalid values', function () {
        $validValues = ['console', 'json', 'html', 'markdown', 'xml', 'csv'];
        $invalidValues = ['invalid', 'unknown', '', 'JSON', 'Html', 'txt', 'pdf'];

        $this->assertEnumFromMethod(OutputFormat::class, $validValues, $invalidValues);
    });

    test('isValid() static method works correctly', function () {
        $validValues = ['console', 'json', 'html', 'markdown', 'xml', 'csv'];
        $invalidValues = ['invalid', 'unknown', '', 'JSON', 'Html', 'txt', 'pdf'];

        $this->assertEnumIsValidMethod(OutputFormat::class, $validValues, $invalidValues);
    });

    test('getSelectOptions() returns properly formatted array', function () {
        $this->assertEnumSelectOptionsMethod(OutputFormat::class);

        $options = OutputFormat::getSelectOptions();
        expect($options)->toHaveCount(6);

        // Verify each option has the correct structure
        foreach ($options as $option) {
            expect($option)->toHaveKeys(['value', 'label']);
            expect(OutputFormat::isValid($option['value']))->toBeTrue();
        }
    });

    test('getExtension() returns correct file extensions', function () {
        $expectedExtensions = [
            'console' => 'txt',
            'json' => 'json',
            'html' => 'html',
            'markdown' => 'md',
            'xml' => 'xml',
            'csv' => 'csv'
        ];

        $this->assertEnumMethodReturns(OutputFormat::class, 'getExtension', $expectedExtensions);
    });

    test('getMimeType() returns correct MIME types', function () {
        $expectedMimeTypes = [
            'console' => 'text/plain',
            'json' => 'application/json',
            'html' => 'text/html',
            'markdown' => 'text/markdown',
            'xml' => 'application/xml',
            'csv' => 'text/csv'
        ];

        $this->assertEnumMethodReturns(OutputFormat::class, 'getMimeType', $expectedMimeTypes);
    });

    test('isStructured() returns correct values', function () {
        $trueCases = ['json', 'xml', 'csv'];
        $falseCases = ['console', 'html', 'markdown'];

        $this->assertEnumBooleanMethod(OutputFormat::class, 'isStructured', $trueCases, $falseCases);
    });

    test('supportsFormatting() returns correct values', function () {
        $trueCases = ['console', 'html', 'markdown'];
        $falseCases = ['json', 'xml', 'csv'];

        $this->assertEnumBooleanMethod(OutputFormat::class, 'supportsFormatting', $trueCases, $falseCases);
    });

    test('isCiCdFriendly() returns correct values', function () {
        $trueCases = ['json', 'xml', 'csv'];
        $falseCases = ['console', 'html', 'markdown'];

        $this->assertEnumBooleanMethod(OutputFormat::class, 'isCiCdFriendly', $trueCases, $falseCases);
    });

    test('getDefaultFilename() returns appropriate filenames', function () {
        $expectedFilenames = [
            'console' => 'validation-output.txt',
            'json' => 'validation-report.json',
            'html' => 'validation-report.html',
            'markdown' => 'VALIDATION_REPORT.md',
            'xml' => 'validation-report.xml',
            'csv' => 'validation-data.csv'
        ];

        $this->assertEnumMethodReturns(OutputFormat::class, 'getDefaultFilename', $expectedFilenames);
    });

    test('getFormatterClass() returns correct class names', function () {
        $expectedClasses = [
            'console' => 'App\\Services\\Formatters\\ConsoleFormatter',
            'json' => 'App\\Services\\Formatters\\JsonFormatter',
            'html' => 'App\\Services\\Formatters\\HtmlFormatter',
            'markdown' => 'App\\Services\\Formatters\\MarkdownFormatter',
            'xml' => 'App\\Services\\Formatters\\XmlFormatter',
            'csv' => 'App\\Services\\Formatters\\CsvFormatter'
        ];

        $this->assertEnumMethodReturns(OutputFormat::class, 'getFormatterClass', $expectedClasses);
    });

    test('getDescription() returns non-empty strings', function () {
        $this->assertEnumDescriptionsNotEmpty(OutputFormat::class);
    });

    test('getHelpText() returns non-empty strings', function () {
        $this->assertEnumHelpTextNotEmpty(OutputFormat::class);
    });

    test('getUseCases() returns arrays of use cases', function () {
        $expectedUseCases = [
            'console' => ['Interactive development', 'Quick validation', 'CI/CD logs'],
            'json' => ['API integration', 'Automated processing', 'Data analysis'],
            'html' => ['Team reports', 'Documentation sites', 'Executive summaries'],
            'markdown' => ['Project documentation', 'README files', 'Wiki pages'],
            'xml' => ['Enterprise integration', 'Data exchange', 'Legacy systems'],
            'csv' => ['Data analysis', 'Spreadsheet import', 'Statistical processing']
        ];

        $this->assertEnumArrayMethod(OutputFormat::class, 'getUseCases', $expectedUseCases);
    });

    test('serialization works correctly', function () {
        $this->assertEnumSerialization(OutputFormat::class);
    });

    test('comparison operations work correctly', function () {
        $this->assertEnumComparisons(OutputFormat::class);
    });

    test('all methods return consistent types', function () {
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getExtension', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getMimeType', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getDefaultFilename', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getFormatterClass', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getDescription', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getHelpText', 'string');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'getUseCases', 'array');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'isStructured', 'bool');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'supportsFormatting', 'bool');
        $this->assertEnumMethodConsistentType(OutputFormat::class, 'isCiCdFriendly', 'bool');
    });

    // Business logic and edge case tests
    test('structured formats are CI/CD friendly', function () {
        foreach (OutputFormat::cases() as $format) {
            if ($format->isStructured()) {
                expect($format->isCiCdFriendly())->toBeTrue(
                    "Structured format {$format->name} should be CI/CD friendly"
                );
            }
        }
    });

    test('formatting and structured are mutually exclusive for most formats', function () {
        foreach (OutputFormat::cases() as $format) {
            // HTML and Markdown are exceptions - they support formatting but aren't structured
            if ($format === OutputFormat::HTML || $format === OutputFormat::MARKDOWN) {
                expect($format->supportsFormatting())->toBeTrue();
                expect($format->isStructured())->toBeFalse();
            } elseif ($format === OutputFormat::CONSOLE) {
                // Console supports formatting but isn't structured
                expect($format->supportsFormatting())->toBeTrue();
                expect($format->isStructured())->toBeFalse();
            } else {
                // JSON, XML, CSV are structured but don't support formatting
                expect($format->isStructured())->toBeTrue();
                expect($format->supportsFormatting())->toBeFalse();
            }
        }
    });

    test('MIME types are valid', function () {
        $validMimePatterns = [
            'text/plain', 'text/html', 'text/markdown', 'text/csv',
            'application/json', 'application/xml'
        ];

        foreach (OutputFormat::cases() as $format) {
            $mimeType = $format->getMimeType();
            expect($mimeType)->toBeIn($validMimePatterns,
                "MIME type {$mimeType} for {$format->name} should be valid"
            );
        }
    });

    test('file extensions match format names where appropriate', function () {
        // Most formats should have extensions that match their names
        $directMatches = [
            OutputFormat::JSON, OutputFormat::HTML,
            OutputFormat::XML, OutputFormat::CSV
        ];

        foreach ($directMatches as $format) {
            expect($format->getExtension())->toBe($format->value);
        }

        // Special cases
        expect(OutputFormat::MARKDOWN->getExtension())->toBe('md');
        expect(OutputFormat::CONSOLE->getExtension())->toBe('txt');
    });

    test('default filenames include appropriate extensions', function () {
        foreach (OutputFormat::cases() as $format) {
            $filename = $format->getDefaultFilename();
            $extension = $format->getExtension();

            expect($filename)->toEndWith(".{$extension}",
                "Default filename {$filename} should end with .{$extension}"
            );
        }
    });

    test('formatter class names follow naming convention', function () {
        foreach (OutputFormat::cases() as $format) {
            $className = $format->getFormatterClass();

            expect($className)->toStartWith('App\\Services\\Formatters\\');
            expect($className)->toEndWith('Formatter');
            expect($className)->toContain(ucfirst($format->value));
        }
    });

    test('use cases are relevant to format capabilities', function () {
        // CI/CD friendly formats should have automation-related use cases
        $ciCdFormats = array_filter(OutputFormat::cases(), fn($f) => $f->isCiCdFriendly());
        foreach ($ciCdFormats as $format) {
            $useCases = $format->getUseCases();
            $useCasesText = implode(' ', $useCases);

            expect($useCasesText)->toMatch('/(API|integration|processing|analysis|automated)/i',
                "CI/CD format {$format->name} should have automation-related use cases"
            );
        }

        // Formatting formats should have presentation-related use cases
        $formattingFormats = array_filter(OutputFormat::cases(), fn($f) => $f->supportsFormatting());
        foreach ($formattingFormats as $format) {
            $useCases = $format->getUseCases();
            $useCasesText = implode(' ', $useCases);

            expect($useCasesText)->toMatch('/(report|documentation|display|interactive|summary)/i',
                "Formatting format {$format->name} should have presentation-related use cases"
            );
        }
    });
});
