<?php

declare(strict_types=1);

namespace Tests\Traits;

trait FileTestHelpers
{
    protected function createTestFile(string $path, string $content): string
    {
        $fullPath = $this->getTestPath($path);
        $directory = dirname($fullPath);

        if (! is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        file_put_contents($fullPath, $content);

        return $fullPath;
    }

    protected function getTestPath(string $path): string
    {
        return __DIR__.'/../fixtures/'.mb_ltrim($path, '/');
    }

    protected function createTestMarkdownFile(string $path, array $links = []): string
    {
        $content = "# Test Document\n\nThis is a test markdown file.\n\n";

        foreach ($links as $text => $url) {
            $content .= "- [{$text}]({$url})\n";
        }

        return $this->createTestFile($path, $content);
    }

    protected function cleanupTestFiles(): void
    {
        $fixturesPath = __DIR__.'/../fixtures';
        if (is_dir($fixturesPath)) {
            $this->deleteDirectory($fixturesPath);
        }
    }

    private function deleteDirectory(string $dir): void
    {
        if (! is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir.DIRECTORY_SEPARATOR.$file;
            is_dir($path) ? $this->deleteDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
}
