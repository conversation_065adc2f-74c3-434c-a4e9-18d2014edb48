<?php

// tests/Feature/Commands/ValidateCommandTest.php

declare(strict_types=1);

use Tests\TestCase;
use Tests\Traits\FileTestHelpers;
use Tests\Traits\HttpTestHelpers;

uses(TestCase::class);

describe('ValidateCommand', function () {
    uses(FileTestHelpers::class, HttpTestHelpers::class);

    it('validates documentation successfully', function () {
        // Create test documentation structure
        $this->createTestMarkdownFile('docs/README.md', [
            'User Guide' => 'guide.md',
            'API Reference' => 'api.md',
        ]);

        $this->createTestFile('docs/guide.md', '# User Guide\n\nComplete guide.');
        $this->createTestFile('docs/api.md', '# API Reference\n\nAPI documentation.');

        // Run validation command
        $this->artisan('validate', ['paths' => ['docs/']])
            ->expectsOutput('✅ All links are valid')
            ->assertExitCode(0);
    });

    it('reports broken links with details', function () {
        $this->createTestMarkdownFile('docs/broken.md', [
            'Missing File' => 'missing.md',
            'Another Missing' => 'also-missing.md',
        ]);

        $this->artisan('validate', ['paths' => ['docs/']])
            ->expectsOutput('❌ Found 2 broken links')
            ->assertExitCode(1);
    });

    it('validates external links when requested', function () {
        $this->createTestMarkdownFile('docs/external.md', [
            'Example Site' => 'https://example.com',
            'GitHub' => 'https://github.com',
        ]);

        $this->mockSuccessfulHttpResponses([
            'https://example.com',
            'https://github.com',
        ]);

        $this->artisan('validate', [
            'paths' => ['docs/'],
            '--check-external' => true,
        ])
            ->expectsOutput('✅ All links are valid')
            ->assertExitCode(0);
    });

    afterEach(function () {
        $this->cleanupTestFiles();
    });
});
