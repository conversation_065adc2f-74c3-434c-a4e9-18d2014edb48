# 7. Testing Documentation Suite

**Date:** July 22, 2025  
**Project:** Laravel Zero validate-links Implementation  
**Compliance:** `.ai/guidelines.md` and `.ai/guidelines/` standards

## 7.1. Overview

This document provides comprehensive testing documentation for the validate-links Laravel Zero application, covering unit, feature, service, performance, and integration tests with complete examples and implementation guidelines.

### 7.1.0. Navigation and Cross-References

**📖 Related Documentation:**
- **[Implementation Guide](020-implementation-guide.md)** - Complete source code for all classes being tested
- **[Code Documentation](060-code-documentation.md)** - Detailed class documentation and usage patterns
- **[CI/CD Testing Pipeline](080-cicd-documentation.md#82-main-testing-pipeline)** - Automated testing in CI/CD
- **[Documentation Index](000-documentation-index.md)** - Cross-reference guide for all components

**🔗 Quick Links:**
- [Service Implementation](020-implementation-guide.md#21-service-implementation-completion) → [Service Testing](#75-service-testing)
- [Command Implementation](020-implementation-guide.md#22-command-implementation-enhancement) → [Command Testing](#73-command-unit-tests)
- [Exception Implementation](020-implementation-guide.md#23-value-objects-and-data-structures) → [Exception Testing](#721-service-unit-tests)
- [CI/CD Setup](080-cicd-documentation.md#82-main-testing-pipeline) → [Integration Testing](#77-integration-testing)

### 7.1.1. Testing Architecture: Hybrid Approach

The validate-links testing infrastructure uses a **hybrid approach** combining minimal base classes with specialized traits for maximum flexibility and maintainability.

**Architecture Components:**
- **Minimal Base Class**: Essential setup and Laravel Zero integration (`TestCase`)
- **Specialized Traits**: Composable functionality for specific testing needs
- **Pest PHP**: Modern testing framework with expressive syntax
- **Custom Expectations**: Domain-specific assertions for validate-links

**Benefits:**
- ✅ **Composition over Inheritance**: Mix and match functionality as needed
- ✅ **Granular Control**: Use only the helpers you need in each test
- ✅ **Laravel Zero Compatibility**: Maintains framework integration
- ✅ **Pest Integration**: Works seamlessly with Pest PHP patterns

### 7.1.2. Testing Framework Components

**Core Framework:**
- **Pest PHP:** Modern testing framework with expressive syntax
- **PHPUnit:** Underlying test runner and assertion library
- **Laravel Testing:** Laravel-specific testing utilities and helpers
- **Mockery:** Mocking framework for dependency isolation

**Testing Traits:**
- **FileTestHelpers**: File creation, cleanup, and markdown generation
- **HttpTestHelpers**: HTTP response mocking and external link testing
- **ValidationTestHelpers**: Configuration creation and result assertions
- **EnumTestHelpers**: Comprehensive enum testing utilities and patterns

### 7.1.3. Testing Traits Implementation

#### FileTestHelpers Trait

**Purpose:** Provides file creation, cleanup, and markdown generation utilities.

**Key Methods:**
- `createTestFile(string $path, string $content)`: Create test files with content
- `createTestMarkdownFile(string $path, array $links)`: Generate markdown with links
- `cleanupTestFiles()`: Clean up test fixtures after tests
- `getTestPath(string $path)`: Get full path to test fixtures

**Usage Example:**
```php
uses(FileTestHelpers::class);

it('creates test markdown files', function () {
    $file = $this->createTestMarkdownFile('test.md', [
        'Link Text' => 'target.md',
        'External' => 'https://example.com'
    ]);

    expect(file_get_contents($file))->toContain('[Link Text](target.md)');
});
```

#### HttpTestHelpers Trait

**Purpose:** Provides HTTP response mocking for external link testing.

**Key Methods:**
- `mockSuccessfulHttpResponses(array $urls)`: Mock successful HTTP responses
- `mockBrokenHttpResponses(array $urls, int $statusCode)`: Mock failed responses
- `mockTimeoutHttpResponses(array $urls)`: Mock timeout scenarios
- `mockMixedHttpResponses(array $responses)`: Mock complex response scenarios

**Usage Example:**
```php
uses(HttpTestHelpers::class);

it('handles external link validation', function () {
    $this->mockSuccessfulHttpResponses(['https://example.com']);

    // Test external link validation logic
    expect($result)->toBeValidationResult();
});
```

#### ValidationTestHelpers Trait

**Purpose:** Provides validation configuration and result assertion utilities.

**Key Methods:**
- `createValidationConfig(array $overrides)`: Create test validation configurations
- `createTestValidationResult(array $overrides)`: Create mock validation results
- `assertValidationSuccess(array $result)`: Assert successful validation
- `assertValidationFailure(array $result, ?int $count)`: Assert validation failures

**Usage Example:**
```php
uses(ValidationTestHelpers::class);

it('validates configuration creation with enums', function () {
    $config = $this->createValidationConfig([
        'scopes' => [ValidationScope::EXTERNAL, ValidationScope::INTERNAL]
    ]);

    expect($config->getScopes())->toContain(ValidationScope::EXTERNAL);
    expect($config->getScopes())->toContain(ValidationScope::INTERNAL);
    expect($config->getTimeout())->toBe(5);
    expect($config->shouldValidateExternal())->toBeTrue();
    expect($config->shouldValidateInternal())->toBeTrue();
});

it('creates validation results with enum status', function () {
    $result = $this->createTestValidationResult([
        'url' => 'https://example.com',
        'status' => LinkStatus::VALID,
        'scope' => ValidationScope::EXTERNAL
    ]);

    $this->assertValidationSuccess($result);
    expect($result->getStatus())->toBe(LinkStatus::VALID);
    expect($result->getScope())->toBe(ValidationScope::EXTERNAL);
});

it('handles failed validation with specific status', function () {
    $result = $this->createFailedValidationResult(
        url: 'https://broken.example',
        status: LinkStatus::NOT_FOUND,
        scope: ValidationScope::EXTERNAL,
        error: 'HTTP 404 Not Found'
    );

    $this->assertValidationFailure($result, LinkStatus::NOT_FOUND);
    expect($result->getError())->toBe('HTTP 404 Not Found');
    expect($result->shouldRetry())->toBeFalse();
});
```

#### EnumTestHelpers Trait

**Purpose:** Provides comprehensive testing utilities for PHP 8.4+ backed enums with type safety and business logic validation.

**Location:** `tests/Traits/EnumTestHelpers.php`

**Key Methods:**
- `assertEnumCasesMatch(string $enumClass, array $expectedCases)`: Verify all enum cases and values
- `assertEnumValuesMethod(string $enumClass, array $expectedValues)`: Test values() static method
- `assertEnumNamesMethod(string $enumClass, array $expectedNames)`: Test names() static method
- `assertEnumIsValidMethod(string $enumClass, array $valid, array $invalid)`: Test isValid() validation
- `assertEnumBooleanMethod(string $enumClass, string $method, array $true, array $false)`: Test boolean methods
- `assertEnumMethodReturns(string $enumClass, string $method, array $expected)`: Test method return values
- `assertEnumSerialization(string $enumClass)`: Test JSON serialization/deserialization
- `assertEnumComparisons(string $enumClass)`: Test enum comparison operations

**Complete Usage Example:**
```php
<?php

declare(strict_types=1);

namespace Tests\Unit\Enums;

use App\Enums\LinkStatus;
use Tests\Traits\EnumTestHelpers;

uses(EnumTestHelpers::class);

describe('LinkStatus Enum', function () {

    test('has all expected cases with correct values', function () {
        $expectedCases = [
            'VALID' => 'valid',
            'BROKEN' => 'broken',
            'TIMEOUT' => 'timeout',
            'FORBIDDEN' => 'forbidden',
            'NOT_FOUND' => 'not_found',
            'INVALID_FORMAT' => 'invalid_format',
            'SECURITY_VIOLATION' => 'security_violation',
            'REDIRECT_LOOP' => 'redirect_loop',
            'SSL_ERROR' => 'ssl_error',
            'DNS_ERROR' => 'dns_error',
        ];

        $this->assertEnumCasesMatch(LinkStatus::class, $expectedCases);
    });

    test('boolean methods return correct values', function () {
        // Test isBroken() method
        $trueCases = ['broken', 'timeout', 'forbidden', 'not_found', 'invalid_format',
                     'security_violation', 'redirect_loop', 'ssl_error', 'dns_error'];
        $falseCases = ['valid'];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'isBroken', $trueCases, $falseCases);

        // Test isTemporary() method
        $temporaryTrue = ['timeout', 'dns_error'];
        $temporaryFalse = ['valid', 'broken', 'forbidden', 'not_found', 'invalid_format',
                          'security_violation', 'redirect_loop', 'ssl_error'];

        $this->assertEnumBooleanMethod(LinkStatus::class, 'isTemporary', $temporaryTrue, $temporaryFalse);
    });

    test('getDescription() returns expected descriptions', function () {
        $expectedDescriptions = [
            'valid' => 'Link is valid and accessible',
            'broken' => 'Link is broken or inaccessible',
            'timeout' => 'Request timed out',
            'forbidden' => 'Access forbidden (403)',
            'not_found' => 'Resource not found (404)',
            'invalid_format' => 'Invalid URL format',
            'security_violation' => 'Security policy violation',
            'redirect_loop' => 'Redirect loop detected',
            'ssl_error' => 'SSL/TLS certificate error',
            'dns_error' => 'DNS resolution failed'
        ];

        $this->assertEnumMethodReturns(LinkStatus::class, 'getDescription', $expectedDescriptions);
    });

    test('business logic validation', function () {
        // Only VALID should not be broken
        expect(LinkStatus::VALID->isBroken())->toBeFalse();

        foreach (LinkStatus::cases() as $case) {
            if ($case !== LinkStatus::VALID) {
                expect($case->isBroken())->toBeTrue("Case {$case->name} should be broken");
            }
        }

        // Temporary statuses should be retryable
        $temporaryStatuses = [LinkStatus::TIMEOUT, LinkStatus::DNS_ERROR];
        foreach ($temporaryStatuses as $status) {
            expect($status->isTemporary())->toBeTrue();
            expect($status->shouldRetry())->toBeTrue();
        }
    });

    test('serialization and comparison work correctly', function () {
        $this->assertEnumSerialization(LinkStatus::class);
        $this->assertEnumComparisons(LinkStatus::class);
    });
});
```

**Enum Testing Best Practices:**

1. **Complete Case Coverage**: Test all enum cases and their values
2. **Method Validation**: Verify all custom methods return expected values
3. **Business Logic**: Test enum-specific business rules and relationships
4. **Type Safety**: Ensure proper type handling and validation
5. **Edge Cases**: Test invalid inputs and boundary conditions
6. **Serialization**: Verify JSON serialization/deserialization works correctly

**Cross-References:**
- **[LinkStatus Tests](../tests/Unit/Enums/LinkStatusTest.php)** - Complete LinkStatus enum test implementation
- **[OutputFormat Tests](../tests/Unit/Enums/OutputFormatTest.php)** - OutputFormat enum test patterns
- **[ValidationScope Tests](../tests/Unit/Enums/ValidationScopeTest.php)** - ValidationScope enum business logic tests
- **[Enums Documentation](100-enums-documentation.md)** - Complete enum implementation details

### 7.1.4. Testing Categories

**Unit Tests:** Test individual classes and methods in isolation
**Feature Tests:** Test complete workflows and command interactions
**Service Tests:** Test service layer business logic and integrations
**Performance Tests:** Benchmark performance and memory usage (see [Advanced Testing Strategies](800-appendices/070-advanced-testing-strategies.md))
**Integration Tests:** Test external dependencies and system integration

## 7.2. Unit Testing with Traits

### 7.2.1. Service Unit Tests with Traits

**Purpose:** Test individual service methods with mocked dependencies using specialized testing traits.

**Location:** `tests/Unit/Services/`

**Enum-Enhanced Trait Usage Pattern:**
```php
<?php

use Tests\TestCase;
use Tests\Traits\{FileTestHelpers, HttpTestHelpers, ValidationTestHelpers};
use App\Enums\{ValidationScope, LinkStatus, OutputFormat};

uses(TestCase::class);

describe('LinkValidationService', function () {
    beforeEach(function () {
        $this->service = app(LinkValidationInterface::class);
    });

    describe('internal link validation', function () {
        uses(FileTestHelpers::class, ValidationTestHelpers::class);

        it('validates existing internal links with enum scopes', function () {
            $this->createTestFile('docs/target.md', '# Target Document');
            $this->createTestMarkdownFile('docs/source.md', [
                'Internal Link' => 'target.md'
            ]);

            $config = $this->createValidationConfigWithScopes(ValidationScope::INTERNAL);
            $results = $this->service->validateFile('docs/source.md', $config);

            expect($results)->toBeArray();
            foreach ($results as $result) {
                expect($result)->toBeInstanceOf(ValidationResult::class);
                expect($result->getScope())->toBe(ValidationScope::INTERNAL);
                $this->assertValidationSuccess($result);
            }
        });

        it('detects broken internal links with specific status', function () {
            $this->createTestMarkdownFile('docs/source.md', [
                'Broken Link' => 'missing.md'
            ]);

            $config = $this->createInternalValidationConfig();
            $results = $this->service->validateFile('docs/source.md', $config);

            expect($results)->toHaveCount(1);
            $result = $results[0];
            $this->assertValidationStatus($result, LinkStatus::NOT_FOUND);
            expect($result->getError())->toContain('File not found');
        });
    });

    describe('external link validation', function () {
        uses(HttpTestHelpers::class, ValidationTestHelpers::class);

        it('validates external links with enum status tracking', function () {
            $urls = ['https://example.com', 'https://github.com'];
            $this->mockSuccessfulHttpResponses($urls);

            $config = $this->createExternalValidationConfig();
            $results = $this->service->validateLinks($urls, $config);

            foreach ($results as $result) {
                expect($result->getScope())->toBe(ValidationScope::EXTERNAL);
                expect($result->getStatus())->toBe(LinkStatus::VALID);
                expect($result->getHttpStatusCode())->toBe(200);
            }
        });

        it('handles various HTTP error statuses with enum mapping', function () {
            $this->mockMixedHttpResponses([
                'https://not-found.example' => ['status' => 404],
                'https://forbidden.example' => ['status' => 403],
                'https://timeout.example' => ['status' => 408],
            ]);

            $config = $this->createExternalValidationConfig();
            $urls = array_keys($this->mockMixedHttpResponses);
            $results = $this->service->validateLinks($urls, $config);

            // Verify status mapping
            $statusMap = [
                'https://not-found.example' => LinkStatus::NOT_FOUND,
                'https://forbidden.example' => LinkStatus::FORBIDDEN,
                'https://timeout.example' => LinkStatus::TIMEOUT,
            ];

            foreach ($results as $result) {
                $expectedStatus = $statusMap[$result->getUrl()];
                $this->assertValidationStatus($result, $expectedStatus);
                expect($result->getConsoleColor())->toBe($expectedStatus->getConsoleColor());
            }
        });
    });

    describe('scope-based validation', function () {
        uses(FileTestHelpers::class, HttpTestHelpers::class, ValidationTestHelpers::class);

        it('validates multiple scopes simultaneously', function () {
            // Create mixed content
            $this->createTestFile('docs/internal.md', '# Internal Doc');
            $this->createTestMarkdownFile('docs/mixed.md', [
                'Internal Link' => 'internal.md',
                'External Link' => 'https://example.com',
                'Anchor Link' => '#section'
            ]);

            $this->mockSuccessfulHttpResponses(['https://example.com']);

            $config = $this->createValidationConfigWithScopes(
                ValidationScope::INTERNAL,
                ValidationScope::EXTERNAL,
                ValidationScope::ANCHOR
            );

            $results = $this->service->validateFile('docs/mixed.md', $config);

            // Group results by scope
            $scopeGroups = [];
            foreach ($results as $result) {
                $scopeGroups[$result->getScope()->value][] = $result;
            }

            expect($scopeGroups)->toHaveKeys([
                ValidationScope::INTERNAL->value,
                ValidationScope::EXTERNAL->value,
                ValidationScope::ANCHOR->value
            ]);

            // Verify all are valid
            foreach ($results as $result) {
                $this->assertValidationSuccess($result);
            }
        });
    });
});
```

**LinkValidationService Unit Test:**

```php
<?php

use App\Services\LinkValidationService;
use App\Services\Contracts\SecurityValidationInterface;
use App\Services\Contracts\GitHubAnchorInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Exceptions\ValidateLinksException;

describe('LinkValidationService', function () {
    beforeEach(function () {
        $this->security = Mockery::mock(SecurityValidationInterface::class);
        $this->anchor = Mockery::mock(GitHubAnchorInterface::class);
        $this->statistics = Mockery::mock(StatisticsInterface::class);
        
        $this->service = new LinkValidationService(
            $this->security,
            $this->anchor,
            $this->statistics
        );
    });

    describe('validateFile', function () {
        it('validates file path security first', function () {
            $filePath = '/path/to/test.md';
            
            $this->security
                ->shouldReceive('validatePath')
                ->with($filePath)
                ->once()
                ->andReturn(false);

            expect(fn() => $this->service->validateFile($filePath, ['internal']))
                ->toThrow(ValidateLinksException::class, 'Invalid file path');
        });

        it('processes valid markdown file successfully', function () {
            $filePath = __DIR__ . '/fixtures/test.md';
            $content = '[Test Link](./example.md)';
            file_put_contents($filePath, $content);

            $this->security
                ->shouldReceive('validatePath')
                ->with($filePath)
                ->andReturn(true);

            $result = $this->service->validateFile($filePath, ['internal']);

            expect($result)->toBeArray()
                ->and($result)->toHaveKey('internal');

            unlink($filePath);
        });
    });

    describe('extractLinks', function () {
        it('extracts markdown links correctly', function () {
            $content = '[Link 1](./file1.md) and [Link 2](https://example.com)';
            
            $links = $this->service->extractLinks($content);
            
            expect($links)->toHaveCount(2)
                ->and($links[0])->toMatchArray([
                    'text' => 'Link 1',
                    'url' => './file1.md',
                    'type' => 'markdown'
                ])
                ->and($links[1])->toMatchArray([
                    'text' => 'Link 2',
                    'url' => 'https://example.com',
                    'type' => 'markdown'
                ]);
        });

        it('extracts HTML links correctly', function () {
            $content = '<a href="./file.md">Internal</a> <a href="https://example.com">External</a>';
            
            $links = $this->service->extractLinks($content);
            
            expect($links)->toHaveCount(2)
                ->and($links[0])->toMatchArray([
                    'text' => 'Internal',
                    'url' => './file.md',
                    'type' => 'html'
                ]);
        });
    });

    describe('categorizeLinks', function () {
        it('categorizes links by type correctly', function () {
            $links = [
                ['url' => './internal.md', 'text' => 'Internal'],
                ['url' => 'https://external.com', 'text' => 'External'],
                ['url' => '#anchor', 'text' => 'Anchor']
            ];

            $categorized = $this->service->categorizeLinks($links);

            expect($categorized)->toHaveKeys(['internal', 'external', 'anchor'])
                ->and($categorized['internal'])->toHaveCount(1)
                ->and($categorized['external'])->toHaveCount(1)
                ->and($categorized['anchor'])->toHaveCount(1);
        });
    });
});
```

**SecurityValidationService Unit Test:**

```php
<?php

use App\Services\SecurityValidationService;

describe('SecurityValidationService', function () {
    beforeEach(function () {
        $this->service = new SecurityValidationService();
    });

    describe('validatePath', function () {
        it('accepts valid relative paths', function () {
            expect($this->service->validatePath('./docs/test.md'))->toBeTrue()
                ->and($this->service->validatePath('docs/test.md'))->toBeTrue();
        });

        it('rejects path traversal attempts', function () {
            expect($this->service->validatePath('../../../etc/passwd'))->toBeFalse()
                ->and($this->service->validatePath('./docs/../../../etc/passwd'))->toBeFalse();
        });

        it('rejects absolute paths outside project', function () {
            expect($this->service->validatePath('/etc/passwd'))->toBeFalse()
                ->and($this->service->validatePath('/tmp/malicious.md'))->toBeFalse();
        });
    });

    describe('validateUrl', function () {
        it('accepts valid HTTP/HTTPS URLs', function () {
            expect($this->service->validateUrl('https://example.com'))->toBeTrue()
                ->and($this->service->validateUrl('http://localhost:8080'))->toBeTrue();
        });

        it('rejects invalid URL formats', function () {
            expect($this->service->validateUrl('not-a-url'))->toBeFalse()
                ->and($this->service->validateUrl('ftp://example.com'))->toBeFalse();
        });

        it('rejects blocked domains', function () {
            // Assuming blocked domains are configured
            expect($this->service->validateUrl('https://malicious-site.com'))->toBeFalse();
        });
    });

    describe('isPathTraversalAttempt', function () {
        it('detects path traversal patterns', function () {
            expect($this->service->isPathTraversalAttempt('../file.md'))->toBeTrue()
                ->and($this->service->isPathTraversalAttempt('../../etc/passwd'))->toBeTrue()
                ->and($this->service->isPathTraversalAttempt('/absolute/path'))->toBeTrue()
                ->and($this->service->isPathTraversalAttempt('~/home/<USER>'))->toBeTrue();
        });

        it('allows safe relative paths', function () {
            expect($this->service->isPathTraversalAttempt('./docs/file.md'))->toBeFalse()
                ->and($this->service->isPathTraversalAttempt('docs/file.md'))->toBeFalse();
        });
    });
});
```

### 7.2.2. Value Object Unit Tests

**ValidationConfig Unit Test:**

```php
<?php

use App\Services\ValueObjects\ValidationConfig;

describe('ValidationConfig', function () {
    describe('fromCommandOptions', function () {
        it('creates config from command options correctly', function () {
            $options = [
                'scope' => ['internal', 'external'],
                'max-depth' => 3,
                'check-external' => true,
                'format' => 'json',
                'verbose' => true
            ];
            $arguments = ['path' => ['docs/', 'README.md']];

            $config = ValidationConfig::fromCommandOptions($options, $arguments);

            expect($config->paths)->toBe(['docs/', 'README.md'])
                ->and($config->scope)->toBe(['internal', 'external'])
                ->and($config->maxDepth)->toBe(3)
                ->and($config->checkExternal)->toBeTrue()
                ->and($config->format)->toBe('json')
                ->and($config->verbose)->toBeTrue();
        });

        it('uses default values for missing options', function () {
            $config = ValidationConfig::fromCommandOptions([], []);

            expect($config->scope)->toBe(['internal', 'anchor'])
                ->and($config->maxDepth)->toBe(0)
                ->and($config->checkExternal)->toBeFalse()
                ->and($config->format)->toBe('console');
        });
    });

    describe('withDefaults', function () {
        it('creates config with default values', function () {
            $config = ValidationConfig::withDefaults();

            expect($config->paths)->toBe([])
                ->and($config->scope)->toBe(['internal', 'anchor'])
                ->and($config->format)->toBe('console')
                ->and($config->showProgress)->toBeTrue();
        });

        it('overrides defaults with provided values', function () {
            $config = ValidationConfig::withDefaults([
                'paths' => ['docs/'],
                'checkExternal' => true,
                'format' => 'html'
            ]);

            expect($config->paths)->toBe(['docs/'])
                ->and($config->checkExternal)->toBeTrue()
                ->and($config->format)->toBe('html');
        });
    });

    describe('with', function () {
        it('creates modified copy with changes', function () {
            $original = ValidationConfig::withDefaults(['format' => 'console']);
            $modified = $original->with(['format' => 'json', 'verbose' => true]);

            expect($original->format)->toBe('console')
                ->and($original->verbose)->toBeFalse()
                ->and($modified->format)->toBe('json')
                ->and($modified->verbose)->toBeTrue();
        });
    });
});
```

**ValidationResult Unit Test:**

```php
<?php

use App\Services\ValueObjects\ValidationResult;

describe('ValidationResult', function () {
    describe('success', function () {
        it('creates successful result correctly', function () {
            $result = ValidationResult::success(
                files: ['file1.md', 'file2.md'],
                links: [['url' => 'link1'], ['url' => 'link2']],
                broken: [],
                statistics: ['processed' => 2],
                duration: 1.5
            );

            expect($result->success)->toBeTrue()
                ->and($result->isSuccessful())->toBeTrue()
                ->and($result->getTotalLinks())->toBe(2)
                ->and($result->getBrokenCount())->toBe(0)
                ->and($result->getSuccessRate())->toBe(100.0);
        });
    });

    describe('failure', function () {
        it('creates failed result correctly', function () {
            $result = ValidationResult::failure(
                files: ['file1.md'],
                links: [['url' => 'link1'], ['url' => 'link2']],
                broken: [['url' => 'broken-link']],
                statistics: ['processed' => 1],
                duration: 2.0,
                error: 'Validation failed'
            );

            expect($result->success)->toBeFalse()
                ->and($result->isSuccessful())->toBeFalse()
                ->and($result->getBrokenCount())->toBe(1)
                ->and($result->getSuccessRate())->toBe(50.0);
        });
    });

    describe('getSummary', function () {
        it('returns formatted summary', function () {
            $result = ValidationResult::success(
                files: ['file1.md', 'file2.md'],
                links: [['url' => 'link1'], ['url' => 'link2'], ['url' => 'link3']],
                broken: [['url' => 'broken']],
                statistics: [],
                duration: 2.5
            );

            $summary = $result->getSummary();

            expect($summary)->toMatchArray([
                'files_processed' => 2,
                'total_links' => 3,
                'broken_links' => 1,
                'success_rate' => 66.67,
                'duration' => 2.5,
                'status' => 'failed'
            ]);
        });
    });
});
```

## 7.3. Command Unit Tests

### 7.3.1. ValidateCommand Unit Test

```php
<?php

use App\Commands\ValidateCommand;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;

describe('ValidateCommand', function () {
    beforeEach(function () {
        $this->validator = Mockery::mock(LinkValidationInterface::class);
        $this->reporter = Mockery::mock(ReportingInterface::class);
    });

    it('handles standard validation mode', function () {
        $command = new ValidateCommand();
        
        // Mock command input
        $command->setInput(new \Symfony\Component\Console\Input\ArrayInput([
            'path' => ['docs/'],
            '--scope' => ['internal'],
            '--format' => 'console'
        ]));

        $this->validator
            ->shouldReceive('validateFile')
            ->andReturn(['internal' => []]);

        $this->reporter
            ->shouldReceive('generateReport')
            ->andReturn(0);

        $result = $command->handle($this->validator, $this->reporter);

        expect($result)->toBe(0);
    });

    it('handles interactive mode', function () {
        $command = new ValidateCommand();
        
        $command->setInput(new \Symfony\Component\Console\Input\ArrayInput([
            'path' => ['docs/'],
            '--interactive' => true
        ]));

        // Mock interactive prompts would be tested here
        // This requires more complex setup for Laravel Prompts

        expect(true)->toBeTrue(); // Placeholder for interactive test
    });
});
```

## 7.4. Feature Testing

### 7.4.1. Complete Validation Workflow Tests

**Purpose:** Test end-to-end validation workflows with real file system interactions.

**Location:** `tests/Feature/ValidationWorkflowTest.php`

```php
<?php

use Illuminate\Support\Facades\File;

describe('Validation Workflow', function () {
    beforeEach(function () {
        $this->testDir = storage_path('testing/validation');
        File::makeDirectory($this->testDir, 0755, true, true);
    });

    afterEach(function () {
        File::deleteDirectory($this->testDir);
    });

    it('validates markdown files with internal links', function () {
        // Create test files
        File::put("{$this->testDir}/index.md", '[Link to page](./page.md)');
        File::put("{$this->testDir}/page.md", '# Page Content');

        $this->artisan('validate', [
            'path' => [$this->testDir],
            '--scope' => ['internal'],
            '--format' => 'json'
        ])
        ->expectsOutput('🔗 Link Validation ✅ PASSED')
        ->assertExitCode(0);
    });

    it('detects broken internal links', function () {
        File::put("{$this->testDir}/index.md", '[Broken Link](./missing.md)');

        $this->artisan('validate', [
            'path' => [$this->testDir],
            '--scope' => ['internal']
        ])
        ->expectsOutput('❌ FAILED')
        ->assertExitCode(1);
    });

    it('validates anchor links correctly', function () {
        $content = "# Heading 1\n[Link to heading](#heading-1)";
        File::put("{$this->testDir}/anchors.md", $content);

        $this->artisan('validate', [
            'path' => [$this->testDir],
            '--scope' => ['anchor']
        ])
        ->expectsOutput('✅ PASSED')
        ->assertExitCode(0);
    });

    it('generates HTML report successfully', function () {
        File::put("{$this->testDir}/test.md", '[Valid Link](./test.md)');
        $reportPath = "{$this->testDir}/report.html";

        $this->artisan('report', [
            'paths' => [$this->testDir],
            '--format' => 'html',
            '--output' => $reportPath
        ])
        ->assertExitCode(0);

        expect(File::exists($reportPath))->toBeTrue()
            ->and(File::get($reportPath))->toContain('<html>')
            ->and(File::get($reportPath))->toContain('Link Validation Report');
    });
});
```

### 7.4.2. Interactive Command Tests

**Location:** `tests/Feature/InteractiveCommandTest.php`

```php
<?php

describe('Interactive Commands', function () {
    it('handles config initialization interactively', function () {
        $this->artisan('config --init')
            ->expectsQuestion('Initialize configuration file?', 'yes')
            ->expectsQuestion('Default validation scope?', ['internal', 'anchor'])
            ->expectsQuestion('Check external links by default?', false)
            ->expectsOutput('Configuration initialized successfully')
            ->assertExitCode(0);
    });

    it('shows current configuration', function () {
        $this->artisan('config --show')
            ->expectsOutput('Current Configuration:')
            ->expectsOutput('Validation Scope: internal, anchor')
            ->assertExitCode(0);
    });
});
```

## 7.5. Service Testing

### 7.5.1. Integration Service Tests

**Purpose:** Test service interactions with real dependencies and external systems.

**Location:** `tests/Service/LinkValidationIntegrationTest.php`

```php
<?php

use App\Services\LinkValidationService;
use App\Services\SecurityValidationService;
use App\Services\GitHubAnchorService;
use App\Services\StatisticsService;

describe('LinkValidation Service Integration', function () {
    beforeEach(function () {
        $this->security = new SecurityValidationService();
        $this->anchor = new GitHubAnchorService();
        $this->statistics = new StatisticsService();

        $this->service = new LinkValidationService(
            $this->security,
            $this->anchor,
            $this->statistics
        );

        $this->testDir = storage_path('testing/integration');
        File::makeDirectory($this->testDir, 0755, true, true);
    });

    afterEach(function () {
        File::deleteDirectory($this->testDir);
    });

    it('validates complex markdown document', function () {
        $content = "
# Main Heading

[Internal Link](./other.md)
[Anchor Link](#main-heading)
[External Link](https://github.com)

## Sub Heading

More content with [another anchor](#sub-heading).
        ";

        File::put("{$this->testDir}/complex.md", $content);
        File::put("{$this->testDir}/other.md", "# Other Document");

        $result = $this->service->validateFile(
            "{$this->testDir}/complex.md",
            ['internal', 'anchor']
        );

        expect($result)->toHaveKeys(['internal', 'anchor'])
            ->and($result['internal'])->toHaveCount(1)
            ->and($result['anchor'])->toHaveCount(2);
    });

    it('handles file system errors gracefully', function () {
        expect(fn() => $this->service->validateFile('/nonexistent/file.md', ['internal']))
            ->toThrow(ValidateLinksException::class);
    });
});
```

---

*This comprehensive testing documentation provides complete examples for all testing categories with practical implementation guidelines.*

---

## 📖 Navigation

**[⬅️ Previous: Code Documentation](060-code-documentation.md)** | **[Next: CI/CD Documentation ➡️](080-cicd-documentation.md)** | **[🔝 Top](#7-testing-documentation-suite)**
