<?php

// bootstrap/app.php - Application initialization

declare(strict_types=1);

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\AppServiceProvider::class,
        App\Providers\ValidateLinksServiceProvider::class,
    ])
    ->withCommands([
        App\Commands\ValidateCommand::class,
        App\Commands\FixCommand::class,
        App\Commands\ReportCommand::class,
        App\Commands\ConfigCommand::class,
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        // Exception handling configuration
    })
    ->create();
