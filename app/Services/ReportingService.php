<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\ReportingInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Services\Formatters\ConsoleFormatter;
use App\Services\Formatters\HtmlFormatter;
use App\Services\Formatters\JsonFormatter;
use App\Services\Formatters\MarkdownFormatter;
use App\Services\ValueObjects\ValidationResult;
use Exception;
use InvalidArgumentException;

final class ReportingService implements ReportingInterface
{
    private array $formatters;

    private StatisticsInterface $statistics;

    public function __construct(StatisticsInterface $statistics)
    {
        $this->statistics = $statistics;
        $this->formatters = [
            'console' => new ConsoleFormatter(),
            'json' => new JsonFormatter(),
            'html' => new HtmlFormatter(),
            'markdown' => new MarkdownFormatter(),
        ];
    }

    /**
     * Generate report from validation results.
     */
    public function generateReport(array $results, string $format = 'console'): string
    {
        if (! isset($this->formatters[$format])) {
            throw new InvalidArgumentException("Unsupported format: {$format}");
        }

        $formatter = $this->formatters[$format];
        $summary = $this->generateSummary($results);

        return $formatter->format($results, $summary);
    }

    /**
     * Add formatter for specific output format.
     */
    public function addFormatter(string $format, object $formatter): void
    {
        $this->formatters[$format] = $formatter;
    }

    /**
     * Export report to file.
     */
    public function exportReport(array $results, string $filePath, string $format = 'json'): bool
    {
        try {
            $report = $this->generateReport($results, $format);

            $directory = dirname($filePath);
            if (! is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            return file_put_contents($filePath, $report) !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get available output formats.
     */
    public function getAvailableFormats(): array
    {
        return array_keys($this->formatters);
    }

    /**
     * Generate summary statistics.
     */
    public function generateSummary(array $results): array
    {
        $total = count($results);
        $valid = 0;
        $invalid = 0;
        $errors = [];

        foreach ($results as $result) {
            if ($result instanceof ValidationResult) {
                if ($result->isValid()) {
                    $valid++;
                } else {
                    $invalid++;
                    if ($result->getError()) {
                        $errors[] = $result->getError();
                    }
                }
            }
        }

        return [
            'total' => $total,
            'valid' => $valid,
            'invalid' => $invalid,
            'success_rate' => $total > 0 ? round(($valid / $total) * 100, 2) : 0,
            'errors' => array_unique($errors),
            'timestamp' => now()->toISOString(),
        ];
    }
}
