<?php

declare(strict_types=1);

namespace App\Services\Contracts;

use App\Enums\LinkStatus;
use App\Enums\OutputFormat;
use App\Services\ValueObjects\ValidationResult;

interface ReportingInterface
{
    /**
     * Generate report from validation results using enum format.
     */
    public function generateReport(array $results, OutputFormat $format = OutputFormat::CONSOLE): string;

    /**
     * Format validation results with specific options.
     *
     * @param  ValidationResult[]  $results
     */
    public function format(array $results, OutputFormat $format, array $options = []): string;

    /**
     * Add formatter for specific output format.
     */
    public function addFormatter(OutputFormat $format, object $formatter): void;

    /**
     * Export report to file with automatic format detection.
     */
    public function exportReport(array $results, string $filePath, ?OutputFormat $format = null): bool;

    /**
     * Get available output formats.
     *
     * @return OutputFormat[]
     */
    public function getAvailableFormats(): array;

    /**
     * Check if format is supported.
     */
    public function supportsFormat(OutputFormat $format): bool;

    /**
     * Generate summary statistics with status breakdown.
     *
     * @param  ValidationResult[]  $results
     * @return array{total_links: int, broken_links: int, success_rate: float, status_breakdown: array<LinkStatus, int>}
     */
    public function generateSummary(array $results): array;

    /**
     * Get formatter instance for specific format.
     */
    public function getFormatter(OutputFormat $format): object;
}
