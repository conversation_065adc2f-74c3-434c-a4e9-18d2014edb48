<?php

// app/Services/PluginManager.php

declare(strict_types=1);

namespace App\Services;

use App\Contracts\ExtensionInterface;
use Illuminate\Support\Collection;

final class PluginManager
{
    private Collection $plugins;

    public function __construct()
    {
        $this->plugins = new Collection();
    }

    public function register(ExtensionInterface $plugin): void
    {
        $this->plugins->put($plugin->getName(), $plugin);
        $plugin->register();
    }

    public function boot(): void
    {
        $this->plugins->each(function (ExtensionInterface $plugin) {
            $plugin->boot();
        });
    }

    public function getPlugin(string $name): ?ExtensionInterface
    {
        return $this->plugins->get($name);
    }
}
