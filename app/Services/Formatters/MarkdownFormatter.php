<?php

declare(strict_types=1);

namespace App\Services\Formatters;

use App\Services\ValueObjects\ValidationResult;

final class MarkdownFormatter
{
    public function format(ValidationResult $result): string
    {
        $output = [];

        $output[] = '# Link Validation Report';
        $output[] = '';
        $output[] = '**Generated:** '.date('Y-m-d H:i:s');
        $output[] = '';

        // Statistics
        $output[] = '## Summary';
        $output[] = '';
        $stats = $result->getStatisticsSummary();
        foreach ($stats as $label => $value) {
            $output[] = "- **{$label}:** {$value}";
        }
        $output[] = '';

        // Status
        if ($result->isSuccessful()) {
            $output[] = '## Status: ✅ Success';
            $output[] = '';
            $output[] = 'All links validated successfully!';
        } else {
            $output[] = '## Status: ❌ Failed';
            $output[] = '';
            $brokenCount = $result->getBrokenCount();
            $output[] = "Found {$brokenCount} broken link(s).";
        }
        $output[] = '';

        // Broken links details
        if ($result->hasBrokenLinks()) {
            $output[] = '## Broken Links';
            $output[] = '';

            $brokenByType = $result->getBrokenLinksByType();

            foreach ($brokenByType as $type => $links) {
                $output[] = "### {$type} Links (".count($links).')</h3>';
                $output[] = '';
                $output[] = '| File | Link | Reason |';
                $output[] = '|------|------|--------|';

                foreach ($links as $link) {
                    $file = basename($link['file']);
                    $url = $link['link'];
                    $reason = $link['reason'] ?? 'Validation failed';
                    $output[] = "| {$file} | `{$url}` | {$reason} |";
                }
                $output[] = '';
            }
        }

        return implode("\n", $output);
    }
}
