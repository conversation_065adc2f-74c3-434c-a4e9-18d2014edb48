<?php

// app/Services/ConcurrentValidationService.php

declare(strict_types=1);

namespace App\Services;

use Generator;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;

final class ConcurrentValidationService
{
    private Client $httpClient;

    private int $concurrency;

    public function __construct(int $concurrency = 10)
    {
        $this->concurrency = $concurrency;
        $this->httpClient = new Client([
            'timeout' => config('validate-links.defaults.timeout'),
            'headers' => [
                'User-Agent' => config('validate-links.scopes.external.user_agent'),
            ],
        ]);
    }

    public function validateExternalLinksConcurrently(array $links): array
    {
        $requests = $this->createRequests($links);
        $results = [];

        $pool = new Pool($this->httpClient, $requests, [
            'concurrency' => $this->concurrency,
            'fulfilled' => function ($response, $index) use (&$results, $links) {
                $results[$index] = $this->processSuccessfulResponse($response, $links[$index]);
            },
            'rejected' => function ($reason, $index) use (&$results, $links) {
                $results[$index] = $this->processFailedResponse($reason, $links[$index]);
            },
        ]);

        $promise = $pool->promise();
        $promise->wait();

        return $results;
    }

    private function createRequests(array $links): Generator
    {
        foreach ($links as $index => $link) {
            yield $index => new Request('HEAD', $link['url']);
        }
    }
}
