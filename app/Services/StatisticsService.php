<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\StatisticsInterface;

final class StatisticsService implements StatisticsInterface
{
    private array $statistics;

    private array $brokenLinks;

    private array $processedFiles;

    public function __construct()
    {
        $this->reset();
    }

    /**
     * Record a validation result.
     */
    public function recordValidation(string $url, bool $isValid, ?string $error = null): void
    {
        $this->statistics['total']++;

        if ($isValid) {
            $this->statistics['valid']++;
        } else {
            $this->statistics['invalid']++;
            $this->brokenLinks[] = [
                'url' => $url,
                'error' => $error,
                'timestamp' => now()->toISOString(),
            ];
        }
    }

    /**
     * Record file processing.
     */
    public function recordFileProcessed(string $filePath, int $linkCount): void
    {
        $this->processedFiles[] = [
            'file' => $filePath,
            'link_count' => $linkCount,
            'timestamp' => now()->toISOString(),
        ];

        $this->statistics['files_processed']++;
        $this->statistics['total_links'] += $linkCount;
    }

    /**
     * Get validation statistics.
     */
    public function getStatistics(): array
    {
        return array_merge($this->statistics, [
            'success_rate' => $this->getSuccessRate(),
            'broken_links_count' => count($this->brokenLinks),
            'processed_files_count' => count($this->processedFiles),
        ]);
    }

    /**
     * Get broken links.
     */
    public function getBrokenLinks(): array
    {
        return $this->brokenLinks;
    }

    /**
     * Get processed files.
     */
    public function getProcessedFiles(): array
    {
        return $this->processedFiles;
    }

    /**
     * Reset statistics.
     */
    public function reset(): void
    {
        $this->statistics = [
            'total' => 0,
            'valid' => 0,
            'invalid' => 0,
            'files_processed' => 0,
            'total_links' => 0,
            'start_time' => now()->toISOString(),
        ];

        $this->brokenLinks = [];
        $this->processedFiles = [];
    }

    /**
     * Get success rate.
     */
    public function getSuccessRate(): float
    {
        if ($this->statistics['total'] === 0) {
            return 0.0;
        }

        return round(($this->statistics['valid'] / $this->statistics['total']) * 100, 2);
    }
}
