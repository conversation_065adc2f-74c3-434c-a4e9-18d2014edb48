<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\GitHubAnchorInterface;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\SecurityValidationInterface;
use App\Services\Contracts\StatisticsInterface;

final class LinkValidationService implements LinkValidationInterface
{
    public function __construct(
        private SecurityValidationInterface $security,
        private GitHubAnchorInterface $anchorService,
        private StatisticsInterface $statistics
    ) {}

    public function validateFile(string $filePath, array $scope): array
    {
        // Validate file path security
        if (! $this->security->validatePath($filePath)) {
            throw new ValidateLinksException("Invalid file path: {$filePath}");
        }

        $content = file_get_contents($filePath);
        $links = $this->extractLinks($content);
        $categorized = $this->categorizeLinks($links);

        $results = [];

        if (in_array('internal', $scope) || in_array('all', $scope)) {
            $results['internal'] = $this->validateInternalLinks(
                $categorized['internal'],
                dirname($filePath)
            );
        }

        if (in_array('external', $scope) || in_array('all', $scope)) {
            $results['external'] = $this->validateExternalLinks($categorized['external']);
        }

        if (in_array('anchor', $scope) || in_array('all', $scope)) {
            $results['anchor'] = $this->validateAnchorLinks($categorized['anchor'], $content);
        }

        return $results;
    }

    public function extractLinks(string $content): array
    {
        $links = [];

        // Extract markdown links [text](url)
        preg_match_all('/\[([^\]]*)\]\(([^)]+)\)/', $content, $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            $links[] = [
                'text' => $match[1],
                'url' => $match[2],
                'type' => 'markdown',
            ];
        }

        // Extract HTML links <a href="url">text</a>
        preg_match_all('/<a\s+href=["\']([^"\']+)["\'][^>]*>([^<]*)<\/a>/i', $content, $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            $links[] = [
                'text' => $match[2],
                'url' => $match[1],
                'type' => 'html',
            ];
        }

        return $links;
    }

    public function categorizeLinks(array $links): array
    {
        $categorized = [
            'internal' => [],
            'external' => [],
            'anchor' => [],
        ];

        foreach ($links as $link) {
            $url = $link['url'];

            if (str_starts_with($url, '#')) {
                $categorized['anchor'][] = $link;
            } elseif (str_starts_with($url, 'http://') || str_starts_with($url, 'https://')) {
                $categorized['external'][] = $link;
            } else {
                $categorized['internal'][] = $link;
            }
        }

        return $categorized;
    }
    // app/Services/LinkValidationService.php - Add missing methods

    public function validateCrossReferences(array $files): array
    {
        $allAnchors = $this->extractAllAnchors($files);
        $crossReferences = [];

        foreach ($files as $file) {
            $links = $this->extractLinks($file);

            foreach ($links as $link) {
                if ($this->isCrossReference($link)) {
                    $crossReferences[] = $this->validateCrossReference($link, $allAnchors);
                }
            }
        }

        return $crossReferences;
    }

    private function extractAllAnchors(array $files): array
    {
        $anchors = [];

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $fileAnchors = $this->gitHubAnchorService->extractAnchors($content);
            $anchors[$file] = $fileAnchors;
        }

        return $anchors;
    }

    private function validateCrossReference(array $link, array $allAnchors): array
    {
        // Implementation for cross-reference validation
        $targetFile = $this->resolveTargetFile($link['url']);
        $targetAnchor = $this->extractAnchorFromUrl($link['url']);

        if (! isset($allAnchors[$targetFile])) {
            return [
                'url' => $link['url'],
                'status' => 'broken',
                'reason' => 'Target file not found',
            ];
        }

        if ($targetAnchor && ! in_array($targetAnchor, $allAnchors[$targetFile])) {
            return [
                'url' => $link['url'],
                'status' => 'broken',
                'reason' => 'Target anchor not found',
            ];
        }

        return [
            'url' => $link['url'],
            'status' => 'valid',
            'reason' => null,
        ];
    }
}
