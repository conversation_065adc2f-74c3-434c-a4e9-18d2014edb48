<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\SecurityValidationInterface;
use InvalidArgumentException;

final class SecurityValidationService implements SecurityValidationInterface
{
    private array $blockedDomains;

    private array $allowedProtocols;

    private int $maxFileSize;

    private array $blockedPaths;

    public function __construct()
    {
        $this->blockedDomains = config('validate-links.security.blocked_domains', []);
        $this->allowedProtocols = config('validate-links.security.allowed_protocols', ['http', 'https']);
        $this->maxFileSize = config('validate-links.security.max_file_size', 50 * 1024 * 1024);
        $this->blockedPaths = [
            '/etc/',
            '/var/',
            '/usr/',
            '/bin/',
            '/sbin/',
            '/root/',
            '/home/',
        ];
    }

    /**
     * Validate if a URL is safe to access.
     */
    public function validateUrl(string $url): bool
    {
        if (empty($url)) {
            throw new InvalidArgumentException('URL cannot be empty');
        }

        $parsedUrl = parse_url($url);

        if ($parsedUrl === false) {
            return false;
        }

        // Check protocol
        if (! $this->isProtocolAllowed($parsedUrl['scheme'] ?? '')) {
            return false;
        }

        // Check domain
        if (isset($parsedUrl['host']) && $this->isDomainBlocked($parsedUrl['host'])) {
            return false;
        }

        // Check for private IP addresses if not allowed
        if (! config('validate-links.security.allow_private_ips', false)) {
            if (isset($parsedUrl['host']) && $this->isPrivateIp($parsedUrl['host'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate if a file path is safe to access.
     */
    public function validatePath(string $path): bool
    {
        if (empty($path)) {
            throw new InvalidArgumentException('Path cannot be empty');
        }

        $realPath = realpath($path);

        if ($realPath === false) {
            return false;
        }

        // Check against blocked paths
        foreach ($this->blockedPaths as $blockedPath) {
            if (str_starts_with($realPath, $blockedPath)) {
                return false;
            }
        }

        // Ensure path is within project directory
        $projectRoot = realpath(base_path());
        if (! str_starts_with($realPath, $projectRoot)) {
            return false;
        }

        return true;
    }

    /**
     * Check if domain is blocked.
     */
    public function isDomainBlocked(string $domain): bool
    {
        return in_array(mb_strtolower($domain), array_map('strtolower', $this->blockedDomains));
    }

    /**
     * Check if protocol is allowed.
     */
    public function isProtocolAllowed(string $protocol): bool
    {
        return in_array(mb_strtolower($protocol), array_map('strtolower', $this->allowedProtocols));
    }

    /**
     * Validate file size limits.
     */
    public function validateFileSize(string $filePath): bool
    {
        if (! file_exists($filePath)) {
            return false;
        }

        $fileSize = filesize($filePath);

        if ($fileSize === false) {
            return false;
        }

        return $fileSize <= $this->maxFileSize;
    }

    /**
     * Get security configuration.
     */
    public function getSecurityConfig(): array
    {
        return [
            'blocked_domains' => $this->blockedDomains,
            'allowed_protocols' => $this->allowedProtocols,
            'max_file_size' => $this->maxFileSize,
            'blocked_paths' => $this->blockedPaths,
        ];
    }

    /**
     * Check if IP address is private.
     */
    private function isPrivateIp(string $host): bool
    {
        $ip = gethostbyname($host);

        if ($ip === $host && ! filter_var($host, FILTER_VALIDATE_IP)) {
            return false; // Not an IP address
        }

        return ! filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
}
