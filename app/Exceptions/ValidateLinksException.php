<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;

/**
 * Base exception class for all validate-links exceptions.
 */
abstract class ValidateLinksException extends Exception
{
    /**
     * Get the error code for this exception.
     */
    abstract public function getErrorCode(): string;

    /**
     * Get the severity level of this exception.
     */
    abstract public function getSeverity(): string;

    /**
     * Get additional context data.
     */
    final public function getContext(): array
    {
        return [];
    }

    /**
     * Convert exception to array format.
     */
    final public function toArray(): array
    {
        return [
            'error_code' => $this->getErrorCode(),
            'message' => $this->getMessage(),
            'severity' => $this->getSeverity(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->getContext(),
        ];
    }
}
