<?php

declare(strict_types=1);

namespace App\Commands;

use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;
use Exception;
use LaravelZero\Framework\Commands\Command;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Throwable;

use function Laravel\Prompts\confirm;
use function Laravel\Prompts\multiselect;
use function Laravel\Prompts\select;
use function Laravel\Prompts\text;

final class ValidateCommand extends BaseValidationCommand
{
    /**
     * The signature of the command.
     */
    protected $signature = 'validate
                            {path? : Path to validate (file or directory)}
                            {--scope=all : Validation scope (internal, external, anchor, image, all)}
                            {--format=console : Output format (console, json, html, markdown)}
                            {--output= : Output file path}
                            {--interactive : Run in interactive mode with guided setup}
                            {--concurrent=10 : Number of concurrent requests (1-50)}
                            {--timeout=30 : Request timeout in seconds (5-300)}
                            {--max-redirects=5 : Maximum redirects to follow (0-10)}
                            {--no-cache : Disable caching for this validation}
                            {--follow-redirects : Follow HTTP redirects}
                            {--user-agent=validate-links/1.0 : Custom User-Agent string}
                            {--verbose : Show detailed output and error messages}';

    /**
     * The description of the command.
     */
    protected $description = 'Validate links in documentation files with comprehensive scope and format options';

    private LinkValidationInterface $linkValidation;

    private ReportingInterface $reporting;

    public function __construct(
        LinkValidationInterface $linkValidation,
        ReportingInterface $reporting
    ) {
        parent::__construct();
        $this->linkValidation = $linkValidation;
        $this->reporting = $reporting;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // Validate command options using enum validation
            $this->validateCommandOptions();

            if ($this->option('interactive')) {
                return $this->handleInteractive();
            }

            return $this->handleNonInteractive();
        } catch (Throwable $e) {
            return $this->handleValidationError($e);
        }
    }

    /**
     * Handle interactive mode with comprehensive configuration.
     */
    private function handleInteractive(): int
    {
        $this->info('🔗 Interactive Link Validation Setup');
        $this->newLine();

        // Gather paths
        $paths = $this->gatherPaths();

        // Gather validation scope
        $scope = $this->gatherValidationScope();

        // Gather output preferences
        $outputConfig = $this->gatherOutputConfiguration();

        // Gather advanced options
        $advancedOptions = $this->gatherAdvancedOptions();

        // Confirm configuration
        if (! $this->confirmConfiguration($paths, $scope, $outputConfig, $advancedOptions)) {
            $this->warn('Validation cancelled.');

            return self::FAILURE;
        }

        // Execute validation with real-time feedback
        return $this->executeInteractiveValidation($paths, $scope, $outputConfig, $advancedOptions);
    }

    /**
     * Gather paths to validate with validation.
     */
    private function gatherPaths(): array
    {
        $paths = [];

        do {
            $path = text(
                label      : 'Enter path to validate',
                placeholder: './docs',
                required   : true,
                validate   : fn (string $value) => is_dir($value) || is_file($value)
                    ? null
                    : 'Path must exist'
            );

            $paths[] = $path;

            $addMore = confirm('Add another path?', false);
        } while ($addMore);

        return $paths;
    }

    /**
     * Gather validation scope using enum-driven options.
     *
     * @return ValidationScope[]
     */
    private function gatherValidationScope(): array
    {
        $this->info('📋 Select validation scopes:');
        $this->newLine();

        // Display scope descriptions
        foreach (ValidationScope::cases() as $scope) {
            if ($scope !== ValidationScope::ALL) {
                $this->line("  <fg=cyan>{$scope->value}</fg>: {$scope->getDescription()}");
            }
        }

        $this->newLine();

        $selectedValues = multiselect(
            label   : 'What types of links should be validated?',
            options : $this->getScopeOptions(),
            default : [ValidationScope::INTERNAL->value, ValidationScope::ANCHOR->value],
            required: true,
            hint    : 'Use space to select, enter to confirm'
        );

        // Convert selected values to enum instances
        return array_map(
            fn ($value) => ValidationScope::from($value),
            $selectedValues
        );
    }

    /**
     * Get scope options formatted for multiselect.
     */
    private function getScopeOptions(): array
    {
        $options = [];

        foreach (ValidationScope::cases() as $scope) {
            if ($scope !== ValidationScope::ALL) {
                $options[$scope->value] = $scope->getDescription();
            }
        }

        return $options;
    }

    /**
     * Gather output configuration using enum-driven options.
     */
    private function gatherOutputConfiguration(): array
    {
        $this->info('📄 Select output format:');
        $this->newLine();

        // Display format descriptions and use cases
        foreach (OutputFormat::cases() as $format) {
            $this->line("  <fg=cyan>{$format->value}</fg>: {$format->getDescription()}");
            $useCases = implode(', ', $format->getUseCases());
            $this->line("    <fg=gray>Use cases: {$useCases}</fg>");
        }

        $this->newLine();

        $formatOptions = [];
        foreach (OutputFormat::cases() as $format) {
            $formatOptions[$format->value] = $format->getDescription();
        }

        $selectedFormat = select(
            label  : 'Choose output format',
            options: $formatOptions,
            default: OutputFormat::CONSOLE->value,
            hint   : 'Each format has different capabilities and use cases'
        );

        $format = OutputFormat::from($selectedFormat);

        $outputFile = null;
        if ($format !== OutputFormat::CONSOLE) {
            $saveToFile = confirm('Save output to file?', true);

            if ($saveToFile) {
                $defaultFilename = $format->getDefaultFilename();
                $outputFile = text(
                    label      : 'Output file path',
                    placeholder: "./reports/{$defaultFilename}",
                    default    : "./reports/{$defaultFilename}",
                    required   : true,
                    validate   : fn (string $value) => $this->validateOutputPath($value, $format)
                );
            }
        }

        return [
            'format' => $format,
            'file' => $outputFile,
        ];
    }

    /**
     * Validate output file path and extension.
     */
    private function validateOutputPath(string $path, OutputFormat $format): ?string
    {
        $expectedExtension = $format->getExtension();
        $actualExtension = pathinfo($path, PATHINFO_EXTENSION);

        if ($actualExtension !== $expectedExtension) {
            return "File extension should be '.{$expectedExtension}' for {$format->value} format";
        }

        $directory = dirname($path);
        if (! is_dir($directory) && ! mkdir($directory, 0755, true)) {
            return "Cannot create directory: {$directory}";
        }

        return null;
    }

    /**
     * Gather advanced validation options.
     */
    private function gatherAdvancedOptions(): array
    {
        $options = [];

        $options['concurrent'] = confirm('Enable concurrent validation?', true);

        if ($options['concurrent']) {
            $options['max_concurrent'] = (int) text(
                label      : 'Maximum concurrent requests',
                placeholder: '10',
                default    : '10',
                validate   : fn (string $value) => is_numeric($value) && (int) $value > 0 && (int) $value <= 50
                    ? null
                    : 'Must be a number between 1 and 50'
            );
        }

        $options['timeout'] = (int) text(
            label      : 'Request timeout (seconds)',
            placeholder: '30',
            default    : '30',
            validate   : fn (string $value) => is_numeric($value) && (int) $value > 0 && (int) $value <= 300
                ? null
                : 'Must be a number between 1 and 300'
        );

        $options['follow_redirects'] = confirm('Follow redirects?', true);

        if ($options['follow_redirects']) {
            $options['max_redirects'] = (int) text(
                label      : 'Maximum redirects to follow',
                placeholder: '5',
                default    : '5',
                validate   : fn (string $value) => is_numeric($value) && (int) $value >= 0 && (int) $value <= 20
                    ? null
                    : 'Must be a number between 0 and 20'
            );
        }

        $options['cache_results'] = confirm('Cache validation results?', true);

        return $options;
    }

    /**
     * Confirm configuration before execution.
     */
    private function confirmConfiguration(array $paths, array $scope, array $outputConfig, array $options): bool
    {
        $this->newLine();
        $this->info('📋 Configuration Summary:');
        $this->line('Paths: '.implode(', ', $paths));
        $this->line('Scope: '.implode(', ', $scope));
        $this->line('Format: '.$outputConfig['format']);
        if ($outputConfig['file']) {
            $this->line('Output: '.$outputConfig['file']);
        }
        $this->line('Concurrent: '.($options['concurrent'] ? 'Yes ('.$options['max_concurrent'].')' : 'No'));
        $this->line('Timeout: '.$options['timeout'].'s');
        $this->newLine();

        return confirm('Proceed with validation?', true);
    }

    /**
     * Execute interactive validation with progress feedback.
     */
    private function executeInteractiveValidation(array $paths, array $scope, array $outputConfig, array $options): int
    {
        $totalFiles = $this->countFiles($paths);

        $progress = progress(
            label: 'Validating links',
            steps: $totalFiles
        );

        $results = [];
        $fileCount = 0;

        foreach ($paths as $path) {
            $files = $this->getFilesFromPath($path);

            foreach ($files as $file) {
                $progress->label('Validating: '.basename($file));

                $fileResults = $this->linkValidationService->validateFile($file, $scope);
                $results[] = $fileResults;

                $fileCount++;
                $progress->advance();

                // Show real-time broken link alerts
                if ($this->hasBrokenLinks($fileResults)) {
                    $this->warn("⚠️  Broken links found in: {$file}");
                }
            }
        }

        $progress->finish();

        // Generate and display/save report
        $report = $this->reportingService->generateReport($results, $outputConfig['format']);

        if ($outputConfig['file']) {
            $this->reportingService->saveReport($report, $outputConfig['file']);
            $this->info("Report saved to: {$outputConfig['file']}");
        } else {
            $this->line($report);
        }

        // Show summary
        $summary = $this->reportingService->generateSummary($results);
        $this->displaySummary($summary);

        return $summary['broken_links'] > 0 ? self::FAILURE : self::SUCCESS;
    }

    /**
     * Count total files to be processed.
     */
    private function countFiles(array $paths): int
    {
        $count = 0;
        foreach ($paths as $path) {
            if (is_file($path)) {
                $count++;
            } elseif (is_dir($path)) {
                $count += count($this->getFilesFromPath($path));
            }
        }

        return $count;
    }

    /**
     * Get files from a given path.
     */
    private function getFilesFromPath(string $path): array
    {
        if (is_file($path)) {
            return [$path];
        }

        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && in_array($file->getExtension(), ['md', 'markdown', 'html', 'htm'])) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Check if file results contain broken links.
     */
    private function hasBrokenLinks(array $fileResults): bool
    {
        foreach ($fileResults as $result) {
            if (isset($result['status']) && $result['status'] === 'broken') {
                return true;
            }
        }

        return false;
    }

    /**
     * Display validation summary.
     */
    private function displaySummary(array $summary): void
    {
        $this->newLine();
        $this->info('📊 Validation Summary:');
        $this->line("Files processed: {$summary['files_processed']}");
        $this->line("Links checked: {$summary['links_checked']}");
        $this->line("Valid links: {$summary['valid_links']}");

        if ($summary['broken_links'] > 0) {
            $this->error("Broken links: {$summary['broken_links']}");
        } else {
            $this->info('Broken links: 0 ✅');
        }

        $this->line("Execution time: {$summary['execution_time']}s");
    }

    /**
     * Handle non-interactive mode.
     */
    private function handleNonInteractive(): int
    {
        $path = $this->argument('path') ?? './docs';
        $scope = $this->option('scope');
        $format = $this->option('format');
        $outputFile = $this->option('output');
        $concurrent = (int) $this->option('concurrent');
        $timeout = (int) $this->option('timeout');

        return $this->performValidation($path, $scope, $format, $outputFile, $concurrent, $timeout);
    }

    /**
     * Perform the actual validation.
     */
    private function performValidation(
        string $path,
        string $scope,
        string $format,
        ?string $outputFile,
        int $concurrent,
        int $timeout
    ): int {
        $this->info("🔍 Validating links in: {$path}");
        $this->info("📋 Scope: {$scope}");
        $this->info("📄 Format: {$format}");

        if ($outputFile) {
            $this->info("💾 Output: {$outputFile}");
        }

        $this->newLine();

        try {
            // Create validation configuration
            $config = new ValidationConfig([
                'scope' => $scope,
                'concurrent_requests' => $concurrent,
                'timeout' => $timeout,
            ]);

            // Perform validation
            $results = $this->linkValidation->validateFile($path, $config);

            // Generate and display report
            $report = $this->reporting->generateReport($results, $format);

            if ($format === 'console') {
                $this->line($report);
            } else {
                $this->info($report);
            }

            // Save to file if requested
            if ($outputFile) {
                $success = $this->reporting->exportReport($results, $outputFile, $format);

                if ($success) {
                    $this->info("✅ Report saved to: {$outputFile}");
                } else {
                    $this->error("❌ Failed to save report to: {$outputFile}");

                    return 1;
                }
            }

            // Show summary
            $summary = $this->reporting->generateSummary($results);
            $this->newLine();
            $this->info('📊 Summary:');
            $this->info("   Total links: {$summary['total']}");
            $this->info("   Valid: {$summary['valid']}");
            $this->info("   Invalid: {$summary['invalid']}");
            $this->info("   Success rate: {$summary['success_rate']}%");

            return $summary['invalid'] > 0 ? 1 : 0;
        } catch (Exception $e) {
            $this->error("❌ Validation failed: {$e->getMessage()}");

            return 1;
        }
    }
}
