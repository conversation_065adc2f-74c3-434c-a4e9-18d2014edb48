<?php

declare(strict_types=1);

namespace App\Commands;

use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;

final class ReportCommand extends BaseValidationCommand
{
    protected $signature = 'report
        {paths* : Paths to analyze}
        {--format=html : Report format (html, markdown, json)}
        {--output= : Output file path}
        {--detailed : Include detailed link analysis}';

    protected $description = 'Generate detailed link validation reports';

    public function handle(
        LinkValidationInterface $validator,
        ReportingInterface $reporter
    ): int {
        $paths = $this->argument('paths');
        if (empty($paths)) {
            $this->error('No paths provided');

            return self::FAILURE;
        }

        $config = ValidationConfig::withDefaults([
            'paths' => $paths,
            'format' => $this->option('format'),
            'output' => $this->option('output'),
        ]);

        $this->info('📊 Generating link validation report');

        $files = $this->collectFiles($paths, $config);
        $result = $validator->validateFiles($files, $config);

        return $reporter->generateReport($result, $config, $this);
    }
}
