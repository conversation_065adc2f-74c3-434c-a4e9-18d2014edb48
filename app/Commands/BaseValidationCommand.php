<?php

declare(strict_types=1);

namespace App\Commands;

use App\Enums\OutputFormat;
use App\Enums\ValidationScope;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;
use LaravelZero\Framework\Commands\Command;
use Throwable;

abstract class BaseValidationCommand extends Command
{
    protected LinkValidationInterface $linkValidation;

    protected ReportingInterface $reporting;

    public function __construct(
        LinkValidationInterface $linkValidation,
        ReportingInterface $reporting
    ) {
        parent::__construct();

        $this->linkValidation = $linkValidation;
        $this->reporting = $reporting;
    }

    /**
     * Create validation configuration from command options.
     */
    protected function createConfigFromOptions(): ValidationConfig
    {
        return ValidationConfig::create([
            'scopes' => $this->parseScopes($this->option('scope')),
            'timeout' => (int) $this->option('timeout', 30),
            'max_redirects' => (int) $this->option('max-redirects', 5),
            'concurrent_requests' => (int) $this->option('concurrency', 10),
            'cache_enabled' => ! $this->option('no-cache'),
            'follow_redirects' => $this->option('follow-redirects'),
            'user_agent' => $this->option('user-agent', 'validate-links/1.0'),
            'output_format' => $this->getOutputFormat(),
        ]);
    }

    /**
     * Parse validation scopes from command option.
     *
     * @return ValidationScope[]
     */
    protected function parseScopes(string $scope): array
    {
        if ($scope === 'all') {
            return ValidationScope::ALL->getIncludedScopes();
        }

        $scopeValues = array_map('trim', explode(',', $scope));

        return array_map(
            fn ($s) => ValidationScope::from($s),
            $scopeValues
        );
    }

    /**
     * Get output format from command option.
     */
    protected function getOutputFormat(): OutputFormat
    {
        return OutputFormat::from($this->option('format', 'console'));
    }

    /**
     * Validate command options using enum validation.
     */
    protected function validateCommandOptions(): void
    {
        // Validate scope option
        $scopeOption = $this->option('scope');
        if ($scopeOption !== 'all' && ! $this->isValidScopeList($scopeOption)) {
            $this->error("Invalid scope: {$scopeOption}");
            $this->info('Valid scopes: '.implode(', ', ValidationScope::values()));
            $this->info('Use "all" for comprehensive validation');
            exit(1);
        }

        // Validate format option
        $formatOption = $this->option('format');
        if (! OutputFormat::isValid($formatOption)) {
            $this->error("Invalid format: {$formatOption}");
            $this->info('Valid formats: '.implode(', ', OutputFormat::values()));
            exit(1);
        }
    }

    /**
     * Process validation results with enum-based status grouping.
     */
    protected function processValidationResults(array $results): int
    {
        $summary = $results['summary'];
        $brokenLinks = $results['broken_links'] ?? [];

        if (empty($brokenLinks)) {
            $this->info('✅ All links are valid!');

            return 0;
        }

        // Group broken links by status using enum
        $statusGroups = $this->groupLinksByStatus($brokenLinks);

        // Display results grouped by status
        $this->displayStatusGroups($statusGroups);

        // Display summary
        $this->displayValidationSummary($summary);

        return 1;
    }

    /**
     * Display validation results using the reporting service.
     */
    protected function displayResults(array $results): int
    {
        $format = $this->getOutputFormat();
        $outputPath = $this->option('output');

        // For console format, use our enhanced display
        if ($format === OutputFormat::CONSOLE && ! $outputPath) {
            return $this->processValidationResults($results);
        }

        // For other formats or file output, use reporting service
        $output = $this->reporting->format($results, $format, [
            'colors' => $format === OutputFormat::CONSOLE,
            'verbose' => $this->option('verbose'),
        ]);

        if ($outputPath) {
            file_put_contents($outputPath, $output);
            $this->info("Results written to: {$outputPath}");
        } else {
            $this->output->write($output);
        }

        return $results['summary']['broken_links'] > 0 ? 1 : 0;
    }

    /**
     * Handle common validation errors.
     */
    protected function handleValidationError(Throwable $e): int
    {
        $this->error("Validation failed: {$e->getMessage()}");

        if ($this->option('verbose')) {
            $this->error($e->getTraceAsString());
        }

        return 2;
    }

    /**
     * Check if scope list contains valid scope values.
     */
    private function isValidScopeList(string $scopeList): bool
    {
        $scopes = array_map('trim', explode(',', $scopeList));

        foreach ($scopes as $scope) {
            if (! ValidationScope::isValid($scope)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Group broken links by LinkStatus enum.
     */
    private function groupLinksByStatus(array $brokenLinks): array
    {
        $statusGroups = [];

        foreach ($brokenLinks as $link) {
            $status = LinkStatus::from($link['status']);
            $statusGroups[$status->value][] = $link;
        }

        return $statusGroups;
    }

    /**
     * Display status groups with enum-based formatting.
     */
    private function displayStatusGroups(array $statusGroups): void
    {
        foreach ($statusGroups as $statusValue => $links) {
            $status = LinkStatus::from($statusValue);
            $this->displayStatusGroup($status, $links);
        }
    }

    /**
     * Display a single status group with colored output.
     */
    private function displayStatusGroup(LinkStatus $status, array $links): void
    {
        $count = count($links);
        $this->newLine();
        $this->line($status->getFormattedDisplay()." ({$count} links)");

        foreach ($links as $link) {
            $this->line("  • {$link['url']} <fg=gray>({$link['file']})</fg>");

            if ($this->option('verbose') && isset($link['error'])) {
                $this->line("    <fg=gray>Error: {$link['error']}</fg>");
            }
        }

        if ($status->shouldRetry()) {
            $this->line("  <fg=yellow>💡 Tip: {$status->getRecommendedAction()}</fg>");
        }
    }

    /**
     * Display validation summary with statistics.
     */
    private function displayValidationSummary(array $summary): void
    {
        $this->newLine();
        $this->line('<fg=white;bg=red> VALIDATION FAILED </fg>');
        $this->line("Files processed: {$summary['files_processed']}");
        $this->line("Total links: {$summary['total_links']}");
        $this->line("Broken links: {$summary['broken_links']}");
        $this->line("Success rate: {$summary['success_rate']}%");
    }
}
